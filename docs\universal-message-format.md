# 医疗文书系统通用PostMessage通信格式

## 设计原则

1. **统一性**：所有医疗文书页面使用相同的消息格式
2. **可扩展性**：支持新增文书类型和操作类型
3. **向后兼容**：保持与现有代码的兼容性
4. **类型安全**：明确的消息结构和字段定义

## 通用消息格式

### 父页面 → iframe 消息格式

```javascript
{
  // 消息标识
  messageType: 'medical_document_action',
  
  // 文书模块标识（用于区分不同的医疗文书组件）
  module: 'admission_notes' | 'progress_notes' | 'discharge_summary' | 'operative_records' | 'informed_consent',
  
  // 操作类型
  action: 'save' | 'delete' | 'print' | 'preview' | 'export' | 'custom',
  
  // 记录标识
  recordId: string | number,
  
  // 扩展数据（可选）
  payload?: {
    // 自定义操作名称（当action为'custom'时使用）
    customAction?: string,
    // 操作参数
    params?: Record<string, any>,
    // 其他扩展字段
    [key: string]: any
  },
  
  // 消息ID（用于追踪和调试）
  messageId: string,
  
  // 时间戳
  timestamp: number
}
```

### iframe → 父页面 消息格式

```javascript
{
  // 消息标识
  messageType: 'medical_document_result',
  
  // 对应的请求消息ID
  requestMessageId: string,
  
  // 文书模块标识
  module: string,
  
  // 操作类型
  action: string,
  
  // 记录标识
  recordId: string | number,
  
  // 操作结果
  result: {
    // 是否成功
    success: boolean,
    // 成功或失败消息
    message?: string,
    // 错误信息
    error?: string,
    // 错误代码
    errorCode?: string,
    // 返回数据
    data?: any
  },
  
  // 时间戳
  timestamp: number
}
```

## 模块标识定义

```javascript
const MODULE_TYPES = {
  ADMISSION_NOTES: 'admission_notes',           // 入院记录
  PROGRESS_NOTES: 'progress_notes',             // 病程记录
  DISCHARGE_SUMMARY: 'discharge_summary',       // 出院记录
  OPERATIVE_RECORDS: 'operative_records',       // 手术记录
  INFORMED_CONSENT: 'informed_consent',         // 知情记录
  MEDICAL_ORDERS: 'medical_orders',             // 医嘱记录
  NURSING_RECORDS: 'nursing_records',           // 护理记录
  EXAMINATION_REPORTS: 'examination_reports',   // 检查报告
  LABORATORY_REPORTS: 'laboratory_reports',     // 检验报告
  CONSULTATION_RECORDS: 'consultation_records'  // 会诊记录
};
```

## 操作类型定义

```javascript
const ACTION_TYPES = {
  SAVE: 'save',           // 保存
  DELETE: 'delete',       // 删除
  PRINT: 'print',         // 打印
  PREVIEW: 'preview',     // 预览
  EXPORT: 'export',       // 导出
  VALIDATE: 'validate',   // 验证
  SUBMIT: 'submit',       // 提交
  APPROVE: 'approve',     // 审批
  REJECT: 'reject',       // 拒绝
  CUSTOM: 'custom'        // 自定义操作
};
```

## 错误代码定义

```javascript
const ERROR_CODES = {
  METHOD_NOT_FOUND: 'METHOD_NOT_FOUND',         // 方法不存在
  PERMISSION_DENIED: 'PERMISSION_DENIED',       // 权限不足
  VALIDATION_FAILED: 'VALIDATION_FAILED',       // 验证失败
  NETWORK_ERROR: 'NETWORK_ERROR',               // 网络错误
  SERVER_ERROR: 'SERVER_ERROR',                 // 服务器错误
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'                // 未知错误
};
```

## 消息示例

### 保存操作请求

```javascript
{
  messageType: 'medical_document_action',
  module: 'admission_notes',
  action: 'save',
  recordId: 'record_123',
  payload: {
    params: {
      autoSave: false,
      validateBeforeSave: true
    }
  },
  messageId: 'msg_' + Date.now() + '_' + Math.random(),
  timestamp: Date.now()
}
```

### 保存操作响应

```javascript
{
  messageType: 'medical_document_result',
  requestMessageId: 'msg_1703123456789_0.123',
  module: 'admission_notes',
  action: 'save',
  recordId: 'record_123',
  result: {
    success: true,
    message: '保存成功',
    data: {
      savedAt: '2023-12-21 10:30:00',
      version: 2
    }
  },
  timestamp: Date.now()
}
```

### 自定义操作请求

```javascript
{
  messageType: 'medical_document_action',
  module: 'progress_notes',
  action: 'custom',
  recordId: 'record_456',
  payload: {
    customAction: 'generateSummary',
    params: {
      includeImages: true,
      format: 'pdf'
    }
  },
  messageId: 'msg_' + Date.now() + '_' + Math.random(),
  timestamp: Date.now()
}
```

## 优势

1. **统一性**：所有医疗文书使用相同的消息格式
2. **可扩展性**：通过module和action字段支持新的文书类型和操作
3. **可追踪性**：messageId和timestamp支持消息追踪和调试
4. **类型安全**：明确的字段定义和枚举值
5. **向后兼容**：可以通过payload字段传递额外信息
6. **错误处理**：统一的错误代码和错误信息格式
