/**
 * 医疗文书通用PostMessage通信Mixin
 * 
 * 提供统一的iframe通信机制，适用于所有医疗文书组件
 * 包括入院记录、病程记录、出院记录、手术记录等
 */

// 消息类型常量
export const MESSAGE_TYPES = {
  ACTION: 'medical_document_action',
  RESULT: 'medical_document_result'
}

// 模块类型常量
export const MODULE_TYPES = {
  ADMISSION_NOTES: 'admission_notes',
  PROGRESS_NOTES: 'progress_notes',
  DISCHARGE_SUMMARY: 'discharge_summary',
  OPERATIVE_RECORDS: 'operative_records',
  INFORMED_CONSENT: 'informed_consent',
  MEDICAL_ORDERS: 'medical_orders',
  NURSING_RECORDS: 'nursing_records'
}

// 操作类型常量
export const ACTION_TYPES = {
  SAVE: 'save',
  DELETE: 'delete',
  PRINT: 'print',
  PREVIEW: 'preview',
  EXPORT: 'export',
  VALIDATE: 'validate',
  SUBMIT: 'submit',
  APPROVE: 'approve',
  REJECT: 'reject',
  CUSTOM: 'custom'
}

// 错误代码常量
export const ERROR_CODES = {
  METHOD_NOT_FOUND: 'METHOD_NOT_FOUND',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
}

/**
 * 医疗文书通信Mixin
 */
export default {
  data() {
    return {
      // 待处理的消息队列
      pendingMessages: new Map(),
      // 消息超时时间（毫秒）
      messageTimeout: 30000
    }
  },

  mounted() {
    this.initMedicalDocumentCommunication()
  },

  beforeDestroy() {
    this.destroyMedicalDocumentCommunication()
  },

  methods: {
    /**
     * 初始化医疗文书通信
     */
    initMedicalDocumentCommunication() {
      window.addEventListener('message', this.handleMedicalDocumentMessage, false)
      console.log(`[${this.getModuleType()}] 医疗文书通信已初始化`)
    },

    /**
     * 销毁医疗文书通信
     */
    destroyMedicalDocumentCommunication() {
      window.removeEventListener('message', this.handleMedicalDocumentMessage, false)
      // 清理待处理的消息
      this.pendingMessages.clear()
      console.log(`[${this.getModuleType()}] 医疗文书通信已销毁`)
    },

    /**
     * 处理医疗文书消息
     * @param {MessageEvent} event 
     */
    handleMedicalDocumentMessage(event) {
      if (!this.isValidMedicalDocumentMessage(event.data)) {
        return
      }

      const { messageType, requestMessageId, module, action, recordId, result } = event.data

      // 只处理结果消息
      if (messageType !== MESSAGE_TYPES.RESULT) {
        return
      }

      // 只处理当前模块的消息
      if (module !== this.getModuleType()) {
        return
      }

      console.log(`[${module}] 收到iframe消息:`, event.data)

      // 查找对应的待处理消息
      const pendingMessage = this.pendingMessages.get(requestMessageId)
      if (!pendingMessage) {
        console.warn(`[${module}] 未找到对应的待处理消息:`, requestMessageId)
        return
      }

      // 清理超时定时器
      if (pendingMessage.timeoutId) {
        clearTimeout(pendingMessage.timeoutId)
      }

      // 从待处理队列中移除
      this.pendingMessages.delete(requestMessageId)

      // 调用结果处理方法
      this.handleMedicalDocumentResult(action, recordId, result, pendingMessage.context)
    },

    /**
     * 验证消息格式
     * @param {any} data 
     * @returns {boolean}
     */
    isValidMedicalDocumentMessage(data) {
      return data && 
             typeof data === 'object' && 
             data.messageType && 
             (data.messageType === MESSAGE_TYPES.ACTION || data.messageType === MESSAGE_TYPES.RESULT)
    },

    /**
     * 发送医疗文书操作指令
     * @param {string} action 操作类型
     * @param {string|number} recordId 记录ID
     * @param {object} options 选项
     * @returns {Promise}
     */
    sendMedicalDocumentAction(action, recordId, options = {}) {
      return new Promise((resolve, reject) => {
        const iframe = this.getMedicalDocumentIframe(recordId)
        if (!iframe || !iframe.contentWindow) {
          reject(new Error('无法访问iframe内容'))
          return
        }

        const messageId = this.generateMessageId()
        const message = {
          messageType: MESSAGE_TYPES.ACTION,
          module: this.getModuleType(),
          action: action,
          recordId: recordId,
          payload: options.payload || {},
          messageId: messageId,
          timestamp: Date.now()
        }

        // 添加到待处理队列
        const pendingMessage = {
          resolve,
          reject,
          context: options.context || {},
          timeoutId: setTimeout(() => {
            this.pendingMessages.delete(messageId)
            reject(new Error(`操作超时: ${action}`))
          }, this.messageTimeout)
        }
        this.pendingMessages.set(messageId, pendingMessage)

        try {
          iframe.contentWindow.postMessage(message, '*')
          console.log(`[${this.getModuleType()}] 发送操作指令:`, message)
        } catch (error) {
          this.pendingMessages.delete(messageId)
          clearTimeout(pendingMessage.timeoutId)
          reject(error)
        }
      })
    },

    /**
     * 处理医疗文书操作结果
     * 子组件需要重写此方法
     * @param {string} action 操作类型
     * @param {string|number} recordId 记录ID
     * @param {object} result 操作结果
     * @param {object} context 上下文信息
     */
    handleMedicalDocumentResult(action, recordId, result, context) {
      console.log(`[${this.getModuleType()}] 处理操作结果:`, { action, recordId, result, context })
      
      if (result.success) {
        this.$message.success(result.message || `${action}成功`)
        this.onMedicalDocumentSuccess(action, recordId, result, context)
      } else {
        this.$message.error(result.error || result.message || `${action}失败`)
        this.onMedicalDocumentError(action, recordId, result, context)
      }
    },

    /**
     * 操作成功回调
     * 子组件可以重写此方法
     */
    onMedicalDocumentSuccess(action, recordId, result, context) {
      // 默认实现：根据操作类型执行相应的后续处理
      switch (action) {
        case ACTION_TYPES.SAVE:
          this.onSaveSuccess && this.onSaveSuccess(recordId, result, context)
          break
        case ACTION_TYPES.DELETE:
          this.onDeleteSuccess && this.onDeleteSuccess(recordId, result, context)
          break
        case ACTION_TYPES.PRINT:
          this.onPrintSuccess && this.onPrintSuccess(recordId, result, context)
          break
      }
    },

    /**
     * 操作失败回调
     * 子组件可以重写此方法
     */
    onMedicalDocumentError(action, recordId, result, context) {
      // 默认实现：记录错误日志
      console.error(`[${this.getModuleType()}] ${action}操作失败:`, result)
    },

    /**
     * 获取模块类型
     * 子组件必须实现此方法
     * @returns {string}
     */
    getModuleType() {
      throw new Error('子组件必须实现getModuleType方法')
    },

    /**
     * 获取医疗文书iframe元素
     * 子组件必须实现此方法
     * @param {string|number} recordId 
     * @returns {HTMLIFrameElement}
     */
    getMedicalDocumentIframe(recordId) {
      throw new Error('子组件必须实现getMedicalDocumentIframe方法')
    },

    /**
     * 生成消息ID
     * @returns {string}
     */
    generateMessageId() {
      return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    },

    // ==================== 便捷方法 ====================

    /**
     * 保存文书
     * @param {string|number} recordId 
     * @param {object} options 
     */
    saveMedicalDocument(recordId, options = {}) {
      return this.sendMedicalDocumentAction(ACTION_TYPES.SAVE, recordId, options)
    },

    /**
     * 删除文书
     * @param {string|number} recordId 
     * @param {object} options 
     */
    deleteMedicalDocument(recordId, options = {}) {
      return this.sendMedicalDocumentAction(ACTION_TYPES.DELETE, recordId, options)
    },

    /**
     * 打印文书
     * @param {string|number} recordId 
     * @param {object} options 
     */
    printMedicalDocument(recordId, options = {}) {
      return this.sendMedicalDocumentAction(ACTION_TYPES.PRINT, recordId, options)
    },

    /**
     * 自定义操作
     * @param {string} customAction 自定义操作名称
     * @param {string|number} recordId 
     * @param {object} options 
     */
    customMedicalDocumentAction(customAction, recordId, options = {}) {
      return this.sendMedicalDocumentAction(ACTION_TYPES.CUSTOM, recordId, {
        ...options,
        payload: {
          ...options.payload,
          customAction
        }
      })
    }
  }
}
