<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="blwsdetail.aspx.cs" Inherits="YourNamespace.blwsdetail" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>医疗文书详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    
    <!-- 引入必要的CSS和JS文件 -->
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/site.css" rel="stylesheet" />
    
    <script src="~/Scripts/jquery-3.6.0.min.js"></script>
    <script src="~/Scripts/bootstrap.min.js"></script>
</head>
<body>
    <form id="form1" runat="server">
        <div class="container-fluid">
            <!-- 页面内容区域 -->
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-header">
                            <h4>医疗文书编辑</h4>
                        </div>
                        <div class="panel-body">
                            <!-- 文书内容编辑区域 -->
                            <div id="documentContent">
                                <!-- 这里是具体的文书编辑内容 -->
                                <asp:TextBox ID="txtContent" runat="server" TextMode="MultiLine" 
                                           Rows="20" CssClass="form-control" placeholder="请输入文书内容..."></asp:TextBox>
                            </div>
                            
                            <!-- 操作按钮区域（仅用于测试，实际操作通过postMessage） -->
                            <div class="btn-group" style="margin-top: 15px;">
                                <button type="button" class="btn btn-primary" onclick="testSave()">测试保存</button>
                                <button type="button" class="btn btn-danger" onclick="testDelete()">测试删除</button>
                                <button type="button" class="btn btn-info" onclick="testPrint()">测试打印</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 隐藏字段用于存储页面参数 -->
        <asp:HiddenField ID="hiddenBLID" runat="server" />
        <asp:HiddenField ID="hiddenWSID" runat="server" />
        <asp:HiddenField ID="hiddenWSLX" runat="server" />
        <asp:HiddenField ID="hiddenGSDM" runat="server" />
    </form>

    <!-- 医疗文书通用PostMessage通信脚本 -->
    <script type="text/javascript">
        // 引入通用通信脚本（实际项目中应该作为单独的JS文件引入）
        
        /**
         * ASP.NET Web Forms 医疗文书通用PostMessage通信脚本
         * 适用于 blwsdetail.aspx 等ASP.NET页面
         */
        window.MedicalDocumentCommunication = (function() {
            'use strict';

            // 消息类型常量
            var MESSAGE_TYPES = {
                ACTION: 'medical_document_action',
                RESULT: 'medical_document_result'
            };

            // 操作类型常量
            var ACTION_TYPES = {
                SAVE: 'save',
                DELETE: 'delete',
                PRINT: 'print',
                PREVIEW: 'preview',
                EXPORT: 'export',
                CUSTOM: 'custom'
            };

            // 错误代码常量
            var ERROR_CODES = {
                METHOD_NOT_FOUND: 'METHOD_NOT_FOUND',
                PERMISSION_DENIED: 'PERMISSION_DENIED',
                VALIDATION_FAILED: 'VALIDATION_FAILED',
                NETWORK_ERROR: 'NETWORK_ERROR',
                SERVER_ERROR: 'SERVER_ERROR',
                UNKNOWN_ERROR: 'UNKNOWN_ERROR'
            };

            var isInitialized = false;
            var currentModule = null;

            /**
             * 初始化通信模块
             */
            function initialize(moduleType) {
                if (isInitialized) return;

                currentModule = moduleType || detectModuleType();
                
                // 添加消息监听器
                if (window.addEventListener) {
                    window.addEventListener('message', handleParentMessage, false);
                } else if (window.attachEvent) {
                    window.attachEvent('onmessage', handleParentMessage);
                }

                isInitialized = true;
                console.log('[' + currentModule + '] ASP.NET医疗文书通信已初始化');
            }

            /**
             * 自动检测模块类型
             */
            function detectModuleType() {
                var params = getUrlParams();
                if (params.as_wslx) {
                    var moduleMap = {
                        '01': 'admission_notes',
                        '02': 'progress_notes', 
                        '03': 'discharge_summary',
                        '04': 'operative_records',
                        '05': 'informed_consent'
                    };
                    return moduleMap[params.as_wslx] || 'medical_document';
                }
                return 'medical_document';
            }

            /**
             * 获取URL参数
             */
            function getUrlParams() {
                var params = {};
                var search = window.location.search.substring(1);
                if (search) {
                    var pairs = search.split('&');
                    for (var i = 0; i < pairs.length; i++) {
                        var pair = pairs[i].split('=');
                        if (pair.length === 2) {
                            params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
                        }
                    }
                }
                return params;
            }

            /**
             * 处理来自父页面的消息
             */
            function handleParentMessage(event) {
                var data = event.data;
                if (!isValidMessage(data) || data.messageType !== MESSAGE_TYPES.ACTION) {
                    return;
                }

                console.log('[' + currentModule + '] 收到父页面消息:', data);
                executeAction(data);
            }

            /**
             * 验证消息格式
             */
            function isValidMessage(data) {
                return data && typeof data === 'object' && 
                       data.messageType && data.module && 
                       data.action && data.messageId;
            }

            /**
             * 执行操作
             */
            function executeAction(message) {
                var action = message.action;
                var result = { success: false, message: '', error: '' };

                try {
                    switch (action) {
                        case ACTION_TYPES.SAVE:
                            result = executeSave(message.payload);
                            break;
                        case ACTION_TYPES.DELETE:
                            result = executeDelete(message.payload);
                            break;
                        case ACTION_TYPES.PRINT:
                            result = executePrint(message.payload);
                            break;
                        case ACTION_TYPES.CUSTOM:
                            result = executeCustomAction(message.payload.customAction, message.payload);
                            break;
                        default:
                            result.error = '不支持的操作类型: ' + action;
                            result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
                    }
                } catch (e) {
                    result.success = false;
                    result.error = e.message || '操作执行失败';
                    result.errorCode = ERROR_CODES.UNKNOWN_ERROR;
                }

                sendResult(message, result);
            }

            /**
             * 执行保存操作
             */
            function executeSave(payload) {
                var result = { success: false, message: '', error: '' };

                if (typeof window.ehr_save === 'function') {
                    try {
                        window.ehr_save();
                        result.success = true;
                        result.message = '保存成功';
                    } catch (e) {
                        result.error = '保存操作失败: ' + e.message;
                    }
                } else {
                    result.error = '页面中没有保存方法';
                    result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
                }

                return result;
            }

            /**
             * 执行删除操作
             */
            function executeDelete(payload) {
                var result = { success: false, message: '', error: '' };

                if (typeof window.ehr_delete === 'function') {
                    try {
                        window.ehr_delete();
                        result.success = true;
                        result.message = '删除成功';
                    } catch (e) {
                        result.error = '删除操作失败: ' + e.message;
                    }
                } else {
                    result.error = '页面中没有删除方法';
                    result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
                }

                return result;
            }

            /**
             * 执行打印操作
             */
            function executePrint(payload) {
                var result = { success: false, message: '', error: '' };

                if (typeof window.ehr_print === 'function') {
                    try {
                        window.ehr_print();
                        result.success = true;
                        result.message = '打印成功';
                    } catch (e) {
                        result.error = '打印操作失败: ' + e.message;
                    }
                } else {
                    result.error = '页面中没有打印方法';
                    result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
                }

                return result;
            }

            /**
             * 执行自定义操作
             */
            function executeCustomAction(customAction, payload) {
                var result = { success: false, message: '', error: '' };

                if (typeof window[customAction] === 'function') {
                    try {
                        var customResult = window[customAction](payload.params);
                        result.success = true;
                        result.message = customAction + '执行成功';
                        result.data = customResult;
                    } catch (e) {
                        result.error = customAction + '执行失败: ' + e.message;
                    }
                } else {
                    result.error = '页面中没有方法: ' + customAction;
                    result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
                }

                return result;
            }

            /**
             * 发送结果给父页面
             */
            function sendResult(originalMessage, result) {
                var resultMessage = {
                    messageType: MESSAGE_TYPES.RESULT,
                    requestMessageId: originalMessage.messageId,
                    module: originalMessage.module,
                    action: originalMessage.action,
                    recordId: originalMessage.recordId,
                    result: result,
                    timestamp: new Date().getTime()
                };

                try {
                    if (window.parent && window.parent !== window) {
                        window.parent.postMessage(resultMessage, '*');
                        console.log('[' + currentModule + '] 发送结果给父页面:', resultMessage);
                    }
                } catch (e) {
                    console.error('[' + currentModule + '] 发送结果失败:', e);
                }
            }

            return {
                initialize: initialize,
                MESSAGE_TYPES: MESSAGE_TYPES,
                ACTION_TYPES: ACTION_TYPES,
                ERROR_CODES: ERROR_CODES
            };
        })();

        // 页面加载完成后自动初始化
        $(document).ready(function() {
            // 延迟初始化，确保页面完全加载
            setTimeout(function() {
                window.MedicalDocumentCommunication.initialize();
            }, 100);
        });

        // ==================== 原有的文书操作方法 ====================
        
        /**
         * 原有的保存方法（会被postMessage调用）
         */
        function ehr_save() {
            // 这里是原有的保存逻辑
            var content = document.getElementById('<%= txtContent.ClientID %>').value;
            if (!content.trim()) {
                throw new Error('文书内容不能为空');
            }
            
            // 模拟保存操作
            console.log('执行保存操作，内容:', content);
            
            // 实际项目中这里应该是AJAX调用后端保存接口
            // $.ajax({
            //     url: 'SaveDocument.ashx',
            //     method: 'POST',
            //     data: { content: content },
            //     success: function(result) {
            //         console.log('保存成功');
            //     },
            //     error: function(xhr, status, error) {
            //         throw new Error('保存失败: ' + error);
            //     }
            // });
        }

        /**
         * 原有的删除方法（会被postMessage调用）
         */
        function ehr_delete() {
            // 这里是原有的删除逻辑
            console.log('执行删除操作');
            
            // 实际项目中这里应该是AJAX调用后端删除接口
        }

        /**
         * 原有的打印方法（会被postMessage调用）
         */
        function ehr_print() {
            // 这里是原有的打印逻辑
            console.log('执行打印操作');
            window.print();
        }

        // ==================== 测试方法 ====================
        
        function testSave() {
            try {
                ehr_save();
                alert('保存成功');
            } catch (e) {
                alert('保存失败: ' + e.message);
            }
        }

        function testDelete() {
            if (confirm('确定要删除吗？')) {
                try {
                    ehr_delete();
                    alert('删除成功');
                } catch (e) {
                    alert('删除失败: ' + e.message);
                }
            }
        }

        function testPrint() {
            try {
                ehr_print();
            } catch (e) {
                alert('打印失败: ' + e.message);
            }
        }
    </script>
</body>
</html>
