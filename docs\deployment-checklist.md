# 医疗文书通用PostMessage通信机制部署清单

## 部署概述

本清单帮助您将统一的postMessage通信机制部署到现有的医疗文书系统中。

## 文件清单

### 核心文件

- ✅ `src/mixins/medical-document-communication.js` - Vue通用通信Mixin
- ✅ `docs/aspnet-iframe-communication.js` - ASP.NET通信脚本
- ✅ `docs/universal-message-format.md` - 消息格式文档
- ✅ `docs/universal-communication-guide.md` - 使用指南

### 示例文件

- ✅ `src/views/patient-inside/inpatient-medical-record/AdmissionNotes.vue` - 重构后的入院记录组件
- ✅ `docs/blwsdetail-aspx-example.aspx` - ASP.NET页面示例
- ✅ `docs/iframe-communication-simple.js` - 简化版通信脚本

## 部署步骤

### 第一阶段：准备工作

#### 1. 备份现有代码
```bash
# 备份相关的Vue组件
cp -r src/views/patient-inside/inpatient-medical-record src/views/patient-inside/inpatient-medical-record.backup

# 备份ASP.NET页面
cp blwsdetail.aspx blwsdetail.aspx.backup
```

#### 2. 部署核心文件
- [ ] 将 `medical-document-communication.js` 复制到 `src/mixins/` 目录
- [ ] 将 `aspnet-iframe-communication.js` 复制到ASP.NET项目的Scripts目录

### 第二阶段：Vue组件迁移

#### 1. 入院记录组件 (AdmissionNotes.vue)
- [ ] 引入通用Mixin
- [ ] 实现必需方法：`getModuleType()` 和 `getMedicalDocumentIframe()`
- [ ] 替换操作方法：`handleSaveClick`、`handleDeleteClick`、`handlePrintClick`
- [ ] 实现成功回调：`onSaveSuccess`、`onDeleteSuccess`、`onPrintSuccess`
- [ ] 测试功能完整性

#### 2. 病程记录组件 (ProgressNotes.vue)
- [ ] 按照入院记录的模式进行重构
- [ ] 设置模块类型为 `MODULE_TYPES.PROGRESS_NOTES`
- [ ] 适配特定的业务逻辑

#### 3. 出院记录组件 (DischargeSummary.vue)
- [ ] 按照入院记录的模式进行重构
- [ ] 设置模块类型为 `MODULE_TYPES.DISCHARGE_SUMMARY`
- [ ] 适配特定的业务逻辑

#### 4. 手术记录组件 (OperativeRecords.vue)
- [ ] 按照入院记录的模式进行重构
- [ ] 设置模块类型为 `MODULE_TYPES.OPERATIVE_RECORDS`
- [ ] 适配特定的业务逻辑

#### 5. 知情记录组件 (InformedConsentSummary.vue)
- [ ] 按照入院记录的模式进行重构
- [ ] 设置模块类型为 `MODULE_TYPES.INFORMED_CONSENT`
- [ ] 适配特定的业务逻辑

### 第三阶段：ASP.NET页面集成

#### 1. blwsdetail.aspx 主页面
- [ ] 在页面底部引入通信脚本
- [ ] 确保原有的 `ehr_save`、`ehr_delete`、`ehr_print` 方法存在
- [ ] 添加页面初始化代码
- [ ] 测试自动模块类型检测

#### 2. 其他相关ASP.NET页面
- [ ] 按需在其他iframe页面中集成通信脚本
- [ ] 确保方法命名的一致性

### 第四阶段：测试验证

#### 1. 功能测试
- [ ] 保存功能测试
  - [ ] 正常保存
  - [ ] 保存失败处理
  - [ ] 保存成功后数据刷新
- [ ] 删除功能测试
  - [ ] 正常删除
  - [ ] 删除确认对话框
  - [ ] 删除成功后列表更新
- [ ] 打印功能测试
  - [ ] 正常打印
  - [ ] 打印失败处理

#### 2. 兼容性测试
- [ ] Chrome浏览器测试
- [ ] Firefox浏览器测试
- [ ] Edge浏览器测试
- [ ] IE11浏览器测试（如需支持）

#### 3. 错误处理测试
- [ ] 网络错误模拟
- [ ] iframe加载失败处理
- [ ] 消息超时处理
- [ ] 权限不足处理

### 第五阶段：性能优化

#### 1. 消息队列优化
- [ ] 检查消息超时设置
- [ ] 优化消息处理逻辑
- [ ] 清理无用的消息监听器

#### 2. 内存管理
- [ ] 确保组件销毁时清理资源
- [ ] 检查是否有内存泄漏

## 验收标准

### 功能验收
- [ ] 所有医疗文书组件的保存、删除、打印功能正常
- [ ] 错误提示信息准确友好
- [ ] 操作成功后UI状态正确更新
- [ ] 跨域安全限制问题已解决

### 性能验收
- [ ] 操作响应时间在可接受范围内（< 3秒）
- [ ] 无明显的内存泄漏
- [ ] 浏览器控制台无错误信息

### 兼容性验收
- [ ] 支持的浏览器版本正常工作
- [ ] 向后兼容性良好
- [ ] 不影响其他功能模块

## 回滚计划

### 快速回滚
如果发现严重问题，可以快速回滚到原有实现：

1. **Vue组件回滚**
```bash
# 恢复备份的组件
cp -r src/views/patient-inside/inpatient-medical-record.backup/* src/views/patient-inside/inpatient-medical-record/
```

2. **ASP.NET页面回滚**
```bash
# 恢复备份的页面
cp blwsdetail.aspx.backup blwsdetail.aspx
```

3. **移除新增文件**
- 删除 `src/mixins/medical-document-communication.js`
- 删除ASP.NET项目中的通信脚本

### 渐进式回滚
如果只有部分功能有问题，可以逐个组件回滚：

1. 保留通用Mixin和ASP.NET脚本
2. 只回滚有问题的Vue组件
3. 修复问题后重新部署

## 监控和维护

### 日志监控
- [ ] 设置错误日志收集
- [ ] 监控通信失败率
- [ ] 跟踪性能指标

### 定期维护
- [ ] 定期检查浏览器兼容性
- [ ] 更新文档和示例
- [ ] 收集用户反馈并优化

## 联系支持

如果在部署过程中遇到问题，请：

1. 查看浏览器控制台的错误信息
2. 检查网络请求是否正常
3. 参考使用指南和示例代码
4. 联系技术支持团队

## 部署完成确认

- [ ] 所有核心文件已部署
- [ ] 所有Vue组件已迁移并测试
- [ ] 所有ASP.NET页面已集成并测试
- [ ] 功能测试通过
- [ ] 性能测试通过
- [ ] 兼容性测试通过
- [ ] 文档已更新
- [ ] 团队已培训

**部署负责人签名：** _________________ **日期：** _________________

**测试负责人签名：** _________________ **日期：** _________________

**项目经理签名：** _________________ **日期：** _________________
