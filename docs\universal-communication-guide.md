# 医疗文书系统通用PostMessage通信机制使用指南

## 概述

本指南介绍了医疗文书系统中统一的postMessage通信机制，该机制解决了iframe跨域安全限制问题，并提供了可扩展的通信架构，适用于所有医疗文书组件。

## 架构设计

### 核心组件

1. **通用Mixin** (`src/mixins/medical-document-communication.js`)
   - 提供统一的通信接口
   - 处理消息队列和超时机制
   - 支持Promise-based的异步操作

2. **ASP.NET通信脚本** (`docs/aspnet-iframe-communication.js`)
   - 适配ASP.NET Web Forms页面
   - 自动检测文书类型
   - 兼容IE8+浏览器

3. **统一消息格式** (`docs/universal-message-format.md`)
   - 标准化的消息结构
   - 支持扩展的操作类型
   - 完善的错误处理机制

## 快速开始

### 1. Vue组件集成

#### 步骤1：引入Mixin

```javascript
import MedicalDocumentCommunication, { 
  MODULE_TYPES, 
  ACTION_TYPES 
} from '@/mixins/medical-document-communication'

export default {
  name: 'YourMedicalComponent',
  mixins: [MedicalDocumentCommunication],
  // ...
}
```

#### 步骤2：实现必需方法

```javascript
methods: {
  // 必需：返回模块类型
  getModuleType() {
    return MODULE_TYPES.ADMISSION_NOTES // 或其他类型
  },

  // 必需：返回iframe元素
  getMedicalDocumentIframe(recordId) {
    return document.getElementById(`iframe_${recordId}`)
  },

  // 可选：操作成功回调
  async onSaveSuccess(recordId, result, context) {
    // 保存成功后的处理逻辑
    await this.refreshData()
  },

  async onDeleteSuccess(recordId, result, context) {
    // 删除成功后的处理逻辑
    this.removeFromList(recordId)
  },

  onPrintSuccess(recordId, result, context) {
    // 打印成功后的处理逻辑
    console.log('打印完成')
  }
}
```

#### 步骤3：使用通信方法

```javascript
methods: {
  async handleSave(recordId) {
    try {
      await this.saveMedicalDocument(recordId, {
        context: { /* 上下文数据 */ }
      })
    } catch (error) {
      this.$message.error('保存失败: ' + error.message)
    }
  },

  async handleDelete(recordId) {
    try {
      await this.deleteMedicalDocument(recordId)
    } catch (error) {
      this.$message.error('删除失败: ' + error.message)
    }
  },

  async handlePrint(recordId) {
    try {
      await this.printMedicalDocument(recordId)
    } catch (error) {
      this.$message.error('打印失败: ' + error.message)
    }
  },

  // 自定义操作
  async handleCustomAction(recordId) {
    try {
      await this.customMedicalDocumentAction('generateSummary', recordId, {
        payload: {
          params: { format: 'pdf', includeImages: true }
        }
      })
    } catch (error) {
      this.$message.error('操作失败: ' + error.message)
    }
  }
}
```

### 2. ASP.NET页面集成

#### 步骤1：引入通信脚本

```html
<!-- 在页面底部引入 -->
<script src="~/Scripts/medical-document-communication.js"></script>
```

#### 步骤2：实现原有方法

```javascript
// 保存方法
function ehr_save() {
    // 原有的保存逻辑
    var content = getDocumentContent();
    if (!content) {
        throw new Error('文书内容不能为空');
    }
    
    // AJAX保存到服务器
    $.ajax({
        url: 'SaveDocument.ashx',
        method: 'POST',
        data: { content: content },
        success: function(result) {
            console.log('保存成功');
        },
        error: function(xhr, status, error) {
            throw new Error('保存失败: ' + error);
        }
    });
}

// 删除方法
function ehr_delete() {
    // 原有的删除逻辑
    $.ajax({
        url: 'DeleteDocument.ashx',
        method: 'POST',
        data: { id: getDocumentId() },
        success: function(result) {
            console.log('删除成功');
        },
        error: function(xhr, status, error) {
            throw new Error('删除失败: ' + error);
        }
    });
}

// 打印方法
function ehr_print() {
    // 原有的打印逻辑
    window.print();
}
```

#### 步骤3：页面初始化

```javascript
$(document).ready(function() {
    // 自动初始化通信模块
    window.MedicalDocumentCommunication.initialize();
});
```

## 支持的操作类型

### 标准操作

- `save` - 保存文书
- `delete` - 删除文书  
- `print` - 打印文书
- `preview` - 预览文书
- `export` - 导出文书
- `validate` - 验证文书
- `submit` - 提交文书
- `approve` - 审批文书
- `reject` - 拒绝文书

### 自定义操作

```javascript
// Vue组件中
await this.customMedicalDocumentAction('customMethodName', recordId, {
  payload: {
    params: { /* 自定义参数 */ }
  }
})

// ASP.NET页面中需要实现对应的方法
function customMethodName(params) {
    // 自定义操作逻辑
    return { /* 返回结果 */ };
}
```

## 错误处理

### 错误类型

- `METHOD_NOT_FOUND` - 方法不存在
- `PERMISSION_DENIED` - 权限不足
- `VALIDATION_FAILED` - 验证失败
- `NETWORK_ERROR` - 网络错误
- `SERVER_ERROR` - 服务器错误
- `UNKNOWN_ERROR` - 未知错误

### 错误处理示例

```javascript
try {
  await this.saveMedicalDocument(recordId)
} catch (error) {
  switch (error.code) {
    case 'METHOD_NOT_FOUND':
      this.$message.error('页面不支持保存功能')
      break
    case 'PERMISSION_DENIED':
      this.$message.error('您没有保存权限')
      break
    case 'VALIDATION_FAILED':
      this.$message.error('数据验证失败')
      break
    default:
      this.$message.error('操作失败: ' + error.message)
  }
}
```

## 调试和监控

### 开启调试日志

```javascript
// 在浏览器控制台中查看详细的通信日志
// 所有消息发送和接收都会有console.log输出
```

### 消息追踪

每个消息都有唯一的`messageId`，可以用于追踪消息的完整生命周期：

```javascript
// 发送消息时会输出
[admission_notes] 发送操作指令: {messageId: "msg_1703123456789_abc123", ...}

// 接收结果时会输出  
[admission_notes] 收到iframe消息: {requestMessageId: "msg_1703123456789_abc123", ...}
```

## 最佳实践

### 1. 错误处理

- 始终使用try-catch包装异步操作
- 提供用户友好的错误提示
- 记录详细的错误日志

### 2. 性能优化

- 避免频繁的消息传递
- 使用上下文参数传递必要信息
- 合理设置消息超时时间

### 3. 兼容性

- 保持原有方法的向后兼容
- 渐进式迁移到新的通信机制
- 测试不同浏览器的兼容性

### 4. 安全性

- 验证消息来源和格式
- 避免传递敏感信息
- 实施适当的权限检查

## 迁移指南

### 从旧版本迁移

1. **保留原有方法**：确保`ehr_save`、`ehr_delete`、`ehr_print`等方法继续存在
2. **引入新的Mixin**：在Vue组件中引入通用通信Mixin
3. **替换调用方式**：将直接调用iframe方法改为使用Mixin提供的方法
4. **测试验证**：确保所有功能正常工作

### 扩展到新组件

1. **复制模式**：参考AdmissionNotes.vue的实现模式
2. **修改模块类型**：设置正确的MODULE_TYPE
3. **实现回调方法**：根据业务需求实现成功回调
4. **添加自定义操作**：如需要，添加特定的自定义操作

## 常见问题

### Q: 如何添加新的操作类型？

A: 在ACTION_TYPES中添加新的常量，然后在ASP.NET页面中实现对应的方法。

### Q: 如何处理超时问题？

A: 可以通过修改`messageTimeout`属性来调整超时时间，默认为30秒。

### Q: 如何支持新的文书类型？

A: 在MODULE_TYPES中添加新的模块类型，并在ASP.NET页面的detectModuleType方法中添加对应的映射。

### Q: 如何调试通信问题？

A: 查看浏览器控制台的日志输出，所有消息发送和接收都有详细记录。
