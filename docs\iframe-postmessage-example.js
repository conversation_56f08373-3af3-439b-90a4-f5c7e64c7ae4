/**
 * iframe页面需要添加的postMessage通信代码示例
 * 
 * 此代码需要添加到iframe页面中（如blwsdetail.aspx等页面）
 * 用于与父页面AdmissionNotes.vue进行通信
 */

// ==================== 消息格式定义 ====================

/**
 * 父页面发送给iframe的消息格式：
 * {
 *   type: 'admission_note_save' | 'admission_note_delete' | 'admission_note_print',
 *   recordId: string | number,  // 记录ID
 *   action: 'save' | 'delete' | 'print'
 * }
 */

/**
 * iframe发送给父页面的消息格式：
 * {
 *   type: 'admission_note_save_result' | 'admission_note_delete_result' | 'admission_note_print_result',
 *   recordId: string | number,  // 记录ID
 *   success: boolean,           // 操作是否成功
 *   message?: string,           // 成功或失败的消息
 *   error?: string              // 错误信息（失败时）
 * }
 */

// ==================== iframe页面代码 ====================

(function() {
  'use strict';

  // 添加message事件监听器
  window.addEventListener('message', handleParentMessage, false);

  /**
   * 处理来自父页面的postMessage消息
   * @param {MessageEvent} event 
   */
  function handleParentMessage(event) {
    // 验证消息来源（可根据需要调整origin验证）
    if (!event.data || typeof event.data !== 'object') {
      return;
    }

    const { type, recordId, action } = event.data;

    // 只处理入院记录相关的消息
    if (!type || !type.startsWith('admission_note_')) {
      return;
    }

    console.log('iframe收到父页面消息:', event.data);

    switch (type) {
      case 'admission_note_save':
        handleSaveAction(recordId);
        break;
      case 'admission_note_delete':
        handleDeleteAction(recordId);
        break;
      case 'admission_note_print':
        handlePrintAction(recordId);
        break;
      default:
        console.warn('iframe收到未知的消息类型:', type);
    }
  }

  /**
   * 处理保存操作
   * @param {string|number} recordId 
   */
  function handleSaveAction(recordId) {
    try {
      // 检查是否存在原有的保存方法
      if (typeof window.ehr_save === 'function') {
        // 调用原有的保存方法
        window.ehr_save();
        
        // 发送成功消息给父页面
        sendMessageToParent({
          type: 'admission_note_save_result',
          recordId: recordId,
          success: true,
          message: '保存成功'
        });
      } else {
        // 如果没有保存方法，发送失败消息
        sendMessageToParent({
          type: 'admission_note_save_result',
          recordId: recordId,
          success: false,
          error: 'iframe中没有保存方法'
        });
      }
    } catch (error) {
      console.error('iframe保存操作失败:', error);
      
      // 发送失败消息给父页面
      sendMessageToParent({
        type: 'admission_note_save_result',
        recordId: recordId,
        success: false,
        error: error.message || '保存操作失败'
      });
    }
  }

  /**
   * 处理删除操作
   * @param {string|number} recordId 
   */
  function handleDeleteAction(recordId) {
    try {
      // 检查是否存在原有的删除方法
      if (typeof window.ehr_delete === 'function') {
        // 调用原有的删除方法
        window.ehr_delete();
        
        // 发送成功消息给父页面
        sendMessageToParent({
          type: 'admission_note_delete_result',
          recordId: recordId,
          success: true,
          message: '删除成功'
        });
      } else {
        // 如果没有删除方法，发送失败消息
        sendMessageToParent({
          type: 'admission_note_delete_result',
          recordId: recordId,
          success: false,
          error: 'iframe中没有删除方法'
        });
      }
    } catch (error) {
      console.error('iframe删除操作失败:', error);
      
      // 发送失败消息给父页面
      sendMessageToParent({
        type: 'admission_note_delete_result',
        recordId: recordId,
        success: false,
        error: error.message || '删除操作失败'
      });
    }
  }

  /**
   * 处理打印操作
   * @param {string|number} recordId 
   */
  function handlePrintAction(recordId) {
    try {
      // 检查是否存在原有的打印方法
      if (typeof window.ehr_print === 'function') {
        // 调用原有的打印方法
        window.ehr_print();
        
        // 发送成功消息给父页面
        sendMessageToParent({
          type: 'admission_note_print_result',
          recordId: recordId,
          success: true,
          message: '打印成功'
        });
      } else {
        // 如果没有打印方法，发送失败消息
        sendMessageToParent({
          type: 'admission_note_print_result',
          recordId: recordId,
          success: false,
          error: 'iframe中没有打印方法'
        });
      }
    } catch (error) {
      console.error('iframe打印操作失败:', error);
      
      // 发送失败消息给父页面
      sendMessageToParent({
        type: 'admission_note_print_result',
        recordId: recordId,
        success: false,
        error: error.message || '打印操作失败'
      });
    }
  }

  /**
   * 发送消息给父页面
   * @param {object} message 
   */
  function sendMessageToParent(message) {
    try {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage(message, '*');
        console.log('iframe发送消息给父页面:', message);
      }
    } catch (error) {
      console.error('发送消息给父页面失败:', error);
    }
  }

  // ==================== 兼容性处理 ====================
  
  /**
   * 为了保持向后兼容性，可以重写原有的方法
   * 使其在执行后自动发送消息给父页面
   */
  
  // 保存原有方法的引用
  const originalEhrSave = window.ehr_save;
  const originalEhrDelete = window.ehr_delete;
  const originalEhrPrint = window.ehr_print;

  // 重写保存方法（可选）
  if (typeof originalEhrSave === 'function') {
    window.ehr_save = function() {
      try {
        originalEhrSave.apply(this, arguments);
        // 可以在这里发送成功消息，但需要知道recordId
        // sendMessageToParent({ type: 'admission_note_save_result', success: true });
      } catch (error) {
        console.error('保存失败:', error);
        // sendMessageToParent({ type: 'admission_note_save_result', success: false, error: error.message });
      }
    };
  }

  console.log('入院记录iframe postMessage通信已初始化');
})();

// ==================== 使用说明 ====================

/**
 * 1. 将此代码添加到iframe页面的底部（在其他脚本加载完成后）
 * 
 * 2. 如果iframe页面使用jQuery，可以在$(document).ready()中初始化
 * 
 * 3. 如果需要自定义消息验证，可以修改handleParentMessage函数中的验证逻辑
 * 
 * 4. 错误处理：所有操作都包含try-catch，确保不会因为错误而中断通信
 * 
 * 5. 调试：所有关键操作都有console.log输出，便于调试
 */
