<template>
  <div class="patient-view">
    <el-tabs v-model="tabActive" type="border-card" @tab-click="handleChangeTab">
      <el-tab-pane name="4" label="我的病人">
        <div class="filter-box">
          <div class="filter-box-left">
            <!--<el-radio-group v-model="orderBy">-->
            <!--  <el-radio :label="1">按病人</el-radio>-->
            <!--  <el-radio :label="2">按床位</el-radio>-->
            <!--</el-radio-group>-->
            <!--<el-button type="primary">清空我的病人</el-button>-->
            <el-button type="primary">打印我的病人</el-button>
          </div>
          <div class="filter-box-right">
            <patient-tips />
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="1" label="治疗组病人">
        <div class="filter-box">
          <div class="filter-box-left">
            <span>治疗组名称： {{ curZhiLiaoZuMC }}</span>
            <el-button type="primary">打印治疗组病人</el-button>
          </div>
          <div class="filter-box-right">
            <div class="filter-box-right">
              <patient-tips />
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="3" label="专科治疗组">
        <div class="filter-box">
          <div class="filter-box-left">
            <span>请选择治疗组：</span>
            <el-select
              :value="zhiLiaoZuID"
              placeholder="选择治疗组"
              size="small"
              @change="handleChangeZhiLiaoZu"
            >
              <el-option
                v-for="item in zhiLiaoZuList"
                :key="item.zhiLiaoZuID"
                :label="item.zhiLiaoZuMC"
                :value="item.zhiLiaoZuID"
              ></el-option>
            </el-select>
          </div>
          <div class="filter-box-right">
            <div class="filter-box-right">
              <patient-tips />
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="2" label="专科病区病人">
        <div class="filter-box">
          <div class="filter-box-left">
            <span>请选择科室：</span>
            <el-select
              v-model="guanLianZKID"
              placeholder="选择科室"
              size="small"
              @change="handleChangeGuanLianZK"
            >
              <el-option
                v-for="item in guanLianZKList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID"
              ></el-option>
            </el-select>
            <span style="margin-left: 10px">请选择病区：</span>
            <el-select
              v-model="bingQuID"
              placeholder="选择病区"
              size="small"
              @change="handleChangeBingQu"
            >
              <el-option
                v-for="item in bingQuList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID"
              ></el-option>
            </el-select>
          </div>
          <div class="filter-box-right">
            <div class="filter-box-right">
              <patient-tips />
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="5" label="公共床位">
        <div class="filter-box">
          <div class="filter-box-left">
            <span>请选择科室：</span>
            <el-select
              v-model="guanLianZKID"
              placeholder="选择科室"
              size="small"
              @change="handleChangeGuanLianZK"
            >
              <el-option
                v-for="item in guanLianZKList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID"
              ></el-option>
            </el-select>
            <span style="margin-left: 10px">请选择病区：</span>
            <el-select
              v-model="bingQuID"
              placeholder="选择病区"
              size="small"
              @change="handleChangeBingQu"
            >
              <el-option
                v-for="item in bingQuList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID"
              ></el-option>
            </el-select>
          </div>
          <div class="filter-box-right">
            <div class="filter-box-right">
              <patient-tips />
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane name="6" label="科研组病人"></el-tab-pane>
    </el-tabs>
    <div v-if="patientList.length === 0" class="empty-info">
      <el-empty description="暂无病人列表"></el-empty>
    </div>
    <el-row v-else class="card-content" type="flex" :gutter="gutter">
      <el-col
        v-for="(patient, index) in patientList"
        :key="index"
        :span="span"
        class="patient-card"
      >
        <el-card
          class="card"
          shadow="hover"
          :body-style="{ padding: '8px', position: 'relative' }"
          @click.native="handleClickPatient(patient)"
          @contextmenu.prevent.native="handleContextMenu($event, patient)"
        >
          <div class="hu-li-jb" :style="{ backgroundColor: huLiJBColor(patient.huLiJBMC) }"></div>
          <div class="card-header">
            <!-- 红色：有传染病史-->
            <div
              class="bed-number"
              :style="{
                color: patient.specialSign && patient.specialSign['传染病史'] ? '#f35656' : ''
              }"
            >
              {{ formatChuangWeiHao(patient.bingQuMC, patient.chuangWeiHao) }}
            </div>
            <div class="patient-basic">
              <div class="bing-ren-info">
                <span class="name" :style="{ fontSize: nameFontSize(patient.bingRenXM) }">
                  {{ patient.bingRenXM }}
                </span>
                <span class="gender">{{ patient.xingBieMC }}</span>
                <span class="age">{{ patient.nianLing }}</span>
              </div>
              <p class="bing-ren-bh">{{ patient.zhuYuanHao }}</p>
            </div>
            <div class="other-info">
              <!-- TODO: 质控第三方接口-->
              <!-- <span class="zhi-kong">{{ patient.zhiKongSL }}</span>-->
              <div
                class="fei-yong-lx"
                :style="{ backgroundColor: feiYongLXColor(patient.jieSuanLXMC) }"
              >
                {{ patient.jieSuanLXMC }}
              </div>
            </div>
          </div>
          <div class="card-body">
            <p class="bing-li-info">
              <span>入院: {{ patient.bingQuRuYuanSJ }}</span>
              <span class="zhu-yuan-sj">{{ patient.zhuYuanSJ }}</span>
              <span class="zhu-yuan-ts">{{ patient.zhuYuanSC }}</span>
            </p>
            <p class="ru-yuan-zd">{{ patient.ruYuanZD }}</p>
          </div>
          <div class="tags">
            <!-- 交通事故-->
            <img
              v-if="patient.specialSign && patient.specialSign['交通事故']"
              :src="require(`@/assets/images/patient-images/patient-card/道路交通事故患者.png`)"
              alt="交通事故"
            />
            <!-- 近期手术-->
            <img
              v-if="patient.specialSign && patient.specialSign['近期手术']"
              :src="require(`@/assets/images/patient-images/patient-card/sstzd.png`)"
              alt="近期手术"
            />
            <!-- 临床路径-->
            <img
              v-if="patient.specialSign && patient.specialSign['临床路径']"
              :src="require(`@/assets/images/patient-images/patient-card/lclj.png`)"
              alt="临床路径"
            />
            <!-- 已书写入院记录-->
            <img
              v-if="patient.specialSign && patient.specialSign['已书写入院记录']"
              :src="require(`@/assets/images/patient-images/patient-card/已书写.png`)"
              alt="已书写"
            />
            <!-- DRG: 0绿色，1黄色，2红色，-1非DRG病例不显示-->
            <img
              v-if="patient.specialSign?.DRG && patient.specialSign?.DRG !== '-1'"
              :src="
                require(`@/assets/images/patient-images/patient-card/drg${patient.specialSign?.DRG}.png`)
              "
              alt="DRG"
            />
            <!-- 多重耐药性-->
            <img
              v-if="patient.specialSign && patient.specialSign['多重耐药性']"
              :src="require(`@/assets/images/patient-images/patient-card/mdro.png`)"
              alt="多重耐药性"
            />
            <!-- 日间手术 待确认，没文档-->
            <!-- 营养风险: LOW黄色 HIGH红色-->
            <img
              v-if="isYYFX(patient)"
              :src="
                require(`@/assets/images/patient-images/patient-card/营养风险_${isYYFX(
                  patient
                )}.png`)
              "
              alt="营养风险"
            />
            <!-- 皮试结果-->
            <el-popover
              v-if="patient.piShiJG && patient.piShiJG.length"
              ref="piShiJG"
              placement="right"
              width="240"
              style="height: 20px"
              trigger="click"
            >
              <div class="pi-shi">
                <span class="title">皮试结果:</span>
                <p v-for="(item, index1) in patient.piShiJG" :key="index1">
                  {{ item.yaoPinMC }}({{ item.piShiJG }})
                </p>
              </div>
              <img
                slot="reference"
                :src="require(`@/assets/images/patient-images/patient-card/过敏及皮试结果.png`)"
                alt="皮试结果"
                @click.stop
              />
            </el-popover>
            <!-- VTE: LOW黄色 HIGH红色-->
            <img
              v-if="isVTE(patient)"
              :src="
                require(`@/assets/images/patient-images/patient-card/VTE_${isVTE(patient)}.png`)
              "
              alt="VTE"
            />
            <!-- 科研标识-->
            <img
              v-if="patient.keYanBR"
              :src="require(`@/assets/images/patient-images/patient-card/keyan.png`)"
              alt="keyan"
            />
          </div>
          <div v-if="tabActive === '4'" class="card-bottom" @click.stop="deleteMyPatient(patient)">
            <i class="el-icon-delete"></i>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!--右键菜单-->
    <right-click-menu
      :class-index="0"
      :right-click-info="rightClickInfo"
      @clickCommand="handleClickContextMenu"
    />
    <!--修改医师备注内容-->
    <doctor-remark
      :doctor-remark.sync="doctorRemark"
      :visible.sync="doctorRemarkVisible"
      @confirm="saveRemark"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getBingqQuListByZhuanKeID,
  getDoctorRemarkByBLID,
  getGuanLianZKListByZhuanKeID,
  getKJYaoWuHZD,
  getMyPatientList,
  getPatientListByBingQu,
  getPatientListByZhiLiaoZu,
  getPublicBedPatientList,
  getResearchPatInfoByDeptId,
  getZhiLiaoZuListByYongHuID,
  joinOrRemoveMyPatient,
  setDoctorRemark
} from '@/api/patient'
import store from '@/store'
import { EventBus } from '@/utils/event-bus'
import PatientTips from '@/views/patient-home/components/PatientTips.vue'
import rightClickMenu from '@/components/RightClickMenu/index'
import DoctorRemark from '@/views/patient-home/components/DoctorRemark.vue'

export default {
  name: 'PatientView',
  components: { PatientTips, rightClickMenu, DoctorRemark },
  data() {
    return {
      gutter: 8,
      span: 4,
      orderBy: 1,
      tabActive: null,
      curZhiLiaoZuMC: '', // 当前治疗组名称
      guanLianZKID: '', // 关联专科id
      guanLianZKList: [], // 关联专科列表
      bingQuID: null,
      bingQuList: [],
      // 右键菜单
      contextMenuVisible: false,
      patientInfo: {},
      rightClickInfo: {},
      doctorRemarkVisible: false,
      doctorRemark: ''
    }
  },
  computed: {
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID,
      geXingHua: ({ patient }) => patient.geXingHua,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      zhiLiaoZuList: ({ patient }) => patient.zhiLiaoZuList,
      patientList: ({ patient }) => patient.patientList,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      keyanbingQuID: ({ patient }) => patient.bingQuID
    })
  },
  async mounted() {
    this.adjustLayout()
    window.addEventListener('resize', this.adjustLayout)
    await store.dispatch('patient/getInit') // 获取初始化信息
    await store.dispatch('patient/getZhiLiaoZuList') // 获取治疗组列表
    await store.dispatch('patient/getBingQuList') // 获取病区列表
    this.tabActive = this.geXingHua[0]?.neiRong || '3' // 个性化显示，找不到则显示专科治疗组
    await this.getPatientList()
    EventBus.$on('updatePatientList', this.getPatientList)
    await this.getKJYaoWuHZDMSg()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.adjustLayout)
    EventBus.$off('updatePatientList')
  },
  methods: {
    adjustLayout() {
      const screenWidth = window.innerWidth
      if (screenWidth <= 1440) {
        this.span = 6
        this.gutter = 6
      } else if (screenWidth <= 1720) {
        this.span = 6
        this.gutter = 16
      } else {
        this.span = 4
        this.gutter = 8
      }
    },
    // 特殊抗菌药物会诊单、三联抗菌药物会诊单通知
    getKJYaoWuHZDMSg() {
      getKJYaoWuHZD().then((res) => {
        if (res.hasError === 0 && res.data) {
          const TsKjywhzdMsg =
            res.data.weiWanChengTsKjywhzdCount !== -1
              ? `【${res.data.weiWanChengTsKjywhzdCount}】条未完成的特殊抗菌药物会诊记录，`
              : ''
          const SlKjywhzdMsg =
            res.data.weiWanChengSlKjywhzdCount !== -1
              ? `【${res.data.weiWanChengSlKjywhzdCount}】条未完成的三联抗菌会诊记录，`
              : ''
          if (TsKjywhzdMsg || SlKjywhzdMsg) {
            this.$alert(
              `您有${SlKjywhzdMsg}${TsKjywhzdMsg}请您于24小时内及时完成。`,
              '抗菌药物会诊单提示',
              {
                confirmButtonText: '确定'
              }
            ).then(() => {
              setTimeout(this.getKJYaoWuHZDMSg, 120 * 60 * 1000) // 120分钟后再次提示
            })
          }
        }
      })
    },
    async handleChangeTab(tab) {
      this.tabActive = tab.name
      if (tab.name === '2' || tab.name === '5') {
        await this.getGuanLianZKList()
      } else {
        await this.getPatientList()
      }
    },
    // 右键事件
    async handleContextMenu(event, patient) {
      this.patientInfo = patient
      const res = await getDoctorRemarkByBLID({ bingLiID: patient.bingLiID })
      if (res.hasError === 0) {
        this.doctorRemark = res.data || ''
        this.rightClickInfo = {
          position: {
            x: event.clientX,
            y: event.clientY
          },
          menuList: [
            {
              key: '1',
              btnName: '检验医嘱'
            },
            {
              key: '2',
              btnName: '检查医嘱'
            },
            {
              key: '3',
              btnName: '院感监控'
            },
            {
              key: '4',
              btnName: '批量预出院'
            },
            {
              key: '5',
              btnName: `备注：${this.doctorRemark || '无'}`
            }
          ]
        }
        this.contextMenuVisible = true
      }
    },
    handleClickPatient(patient) {
      this.patientInfo = patient
      this.$router.push({
        path: `/patient-detail/${patient.bingLiID}`,
        query: {
          title: `${patient.jieSuanLXMC} ${this.formatChuangWeiHao(
            patient.bingQuMC,
            patient.chuangWeiHao
          )} ${patient.bingRenXM}`
        }
      })
    },
    // 点击右键菜单项
    handleClickContextMenu(key) {
      console.log('当前点击的菜单项为：', key)
      switch (key) {
        case '1':
          break
        case '2':
          break
        case '3':
          break
        case '4':
          break
        case '5':
          this.doctorRemarkVisible = true
          break
      }
    },
    // 保存备注
    saveRemark(value) {
      if (value) {
        setDoctorRemark({ beiZhu: value, bingLiID: this.patientInfo.bingLiID }).then((res) => {
          if (res.hasError === 0) {
            this.$message.success('保存成功')
            this.doctorRemarkVisible = false
          } else {
            this.$message.error('保存失败')
          }
        })
      } else {
        this.$message.error('请输入备注内容')
      }
    },
    async getPatientList() {
      let res
      await this.$store.dispatch('patient/setPatientList', [])
      switch (this.tabActive) {
        case '4': // 我的病人
          res = await getMyPatientList({ yongHuID: this.yongHuID })
          if (res.hasError === 0) {
            await this.$store.dispatch('patient/setPatientList', res.data)
          }
          break
        case '1': // 治疗组病人
          const zlz = await getZhiLiaoZuListByYongHuID()
          if (zlz.hasError === 0 && zlz.data.length) {
            let curZhiLiaoZuID = zlz.data[0].zhiLiaoZuID
            this.curZhiLiaoZuMC = zlz.data[0].zhiLiaoZuMC
            res = await getPatientListByZhiLiaoZu({
              zhiLiaoZID: curZhiLiaoZuID,
              zhuanKeID: this.zhuanKeID
            })
            if (res.hasError === 0) {
              await this.$store.dispatch('patient/setPatientList', res.data)
            }
          }
          break
        case '3': // 专科治疗组病人
          if (this.zhuanKeID && this.zhiLiaoZuID) {
            res = await getPatientListByZhiLiaoZu({
              zhiLiaoZID: this.zhiLiaoZuID,
              zhuanKeID: this.zhuanKeID
            })
            if (res.hasError === 0) {
              await this.$store.dispatch('patient/setPatientList', res.data)
            }
          }
          break
        case '2': // 专科病区病人
          if (this.guanLianZKID && this.bingQuID) {
            res = await getPatientListByBingQu({
              zhuanKeID: this.guanLianZKID,
              bingQuID: this.bingQuID
            })
            if (res.hasError === 0) {
              await this.$store.dispatch('patient/setPatientList', res.data)
            }
          }
          break
        case '5': // 公共床位病人
          if (this.guanLianZKID && this.bingQuID) {
            res = await getPublicBedPatientList({
              zhuanKeID: this.guanLianZKID,
              bingQuID: this.bingQuID
            })
            if (res.hasError === 0) {
              await this.$store.dispatch('patient/setPatientList', res.data)
            }
          }
          break
        case '6': // 科研组病人
          if (this.zhuanKeID && this.keyanbingQuID) {
            res = await getResearchPatInfoByDeptId({
              zhuanKeID: this.zhuanKeID,
              bingQuID: this.keyanbingQuID
            })
            if (res.hasError === 0) {
              await this.$store.dispatch('patient/setPatientList', res.data)
            }
          }
          break
        default:
          break
      }
    },
    // 获取关联专科列表
    async getGuanLianZKList() {
      const res = await getGuanLianZKListByZhuanKeID({ zhuanKeID: this.zhuanKeID })
      if (res.hasError === 0) {
        this.guanLianZKList = res.data
        if (this.guanLianZKList.length) {
          this.guanLianZKID = this.guanLianZKList[0].buMenID
          const res = await getBingqQuListByZhuanKeID({ ZKID: this.guanLianZKID })
          if (res.hasError === 0) {
            this.bingQuList = res.data
            if (this.bingQuList.length) {
              this.bingQuID = this.bingQuList[0].buMenID
            }
          }
        }
      }
      await this.getPatientList()
    },
    async handleChangeGuanLianZK(value) {
      this.guanLianZKID = value
      this.bingQuID = null
      const res = await getBingqQuListByZhuanKeID({ ZKID: value })
      if (res.hasError === 0) {
        this.bingQuList = res.data
        if (this.bingQuList.length) {
          this.bingQuID = this.bingQuList[0].buMenID
        }
      }
      await this.getPatientList()
    },
    async handleChangeBingQu(value) {
      this.bingQuID = value
      await this.getPatientList()
    },
    nameFontSize(value) {
      return value.length <= 3 ? '18px' : value.length <= 4 ? '16px' : '13px'
    },
    // 特殊护理:紫色; 一级护理:红色; 二级护理:黄色; 三级护理:绿色; 无护理级别:蓝色;
    huLiJBColor(huLiJB) {
      switch (huLiJB) {
        case '特殊护理':
          return '#a66dd4'
        case '一级护理':
          return '#f35656'
        case '二级护理':
          return '#edcf0c'
        case '三级护理':
          return '#66be74'
        default:
          return '#d4e1fa'
      }
    },
    feiYongLXColor(feiYongLX) {
      switch (feiYongLX) {
        case '自费':
          return '#3b76ef'
        case '社保':
          return '#66be74'
        default:
          return '#d4e1fa'
      }
    },
    formatChuangWeiHao(bingQuMC, chuangWeiHao) {
      return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
    },
    isYYFX(item, type) {
      if (!item || !item.assessmentRecords) {
        return false
      }
      // 判断是否为营养风险 找不到返回false
      const obj = item.assessmentRecords.find((i) => i.type === 'YYSC')
      return obj?.riskLevel ?? false
    },
    isVTE(item, name) {
      if (!item || !item.assessmentRecords) {
        return false
      }
      // 判断是否为padua和caprini 找不到返回false
      const obj = item.assessmentRecords.find((i) => i.name === 'padua' || i.name === 'caprini')
      return obj?.riskLevel ?? false
    },
    async handleChangeZhiLiaoZu(value) {
      await this.$store.dispatch('patient/setZhiLiaoZuID', value)
      await this.getPatientList()
    },
    deleteMyPatient(patient) {
      joinOrRemoveMyPatient({ bingLiID: patient.bingLiID, leiBie: 0 }).then((res) => {
        if (res.hasError === 0) {
          this.getPatientList()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/theme/normal';
.el-tabs--border-card {
  border-top: none;
}

.patient-view {
  width: 100%;
  .filter-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid $--border-color-base;
    border-radius: 4px;
    padding: 13px 12px;
    .filter-box-right {
      .el-alert {
        padding: 6px 10px;
        font-size: 12px;
        border: 1px solid rgba(21, 91, 212, 0.45);
        background-color: rgba(21, 91, 212, 0.05);
      }
    }
  }
}

.empty-info {
  margin: 0 auto;
}

.card-content {
  flex-flow: wrap;
  overflow-y: auto;
  max-height: calc(100% - 145px);

  .patient-card {
    min-height: 163px;
    margin-top: 8px;

    .card {
      border-radius: 8px;
      overflow: hidden;
      height: 100%;
      border: 1px solid $--border-color-base;
      cursor: pointer;

      .hu-li-jb {
        position: absolute;
        top: 0;
        right: 0;
        border-top-right-radius: 8px;
        width: 140px;
        height: 6px;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 8px;
        border-bottom: 1px solid #eceef1;
        font-size: 14px;

        .bed-number {
          width: 83px;
          height: 44px;
          line-height: 44px;
          font-size: 18px;
          font-weight: bold;
          color: $--color-text-primary;
          text-align: center;
          border-radius: 4px;
          box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.15);
          overflow: hidden;
          white-space: nowrap;

          &:hover {
            box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.3);
          }
        }

        .patient-basic {
          display: flex;
          flex-direction: column;

          .bing-ren-info {
            display: flex;
            flex-flow: wrap;
            justify-content: flex-start;
            align-items: center;
            gap: 4px;

            .name {
              font-weight: bold;
              max-width: 65px;
              color: $--color-text-regular;
            }
          }
        }

        .other-info {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: 4px;

          .zhi-kong {
            color: $color-danger;
            font-weight: bold;
            background-color: rgba($color-danger, 0.2);
            padding: 2px 4px;
            border-radius: 2px;
          }

          .fei-yong-lx {
            color: #fff;
            background-color: $color-primary-blue;
            padding: 0 4px;
            border-radius: 2px;
          }
        }
      }

      .card-body {
        padding: 6px 0;
        border-bottom: 1px solid #eceef1;

        .bing-li-info {
          color: $--color-text-secondary;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .zhu-yuan-sj {
            color: $color-danger;
          }
          .zhu-yuan-ts {
            color: rgb(243, 86, 86);
          }
        }

        .ru-yuan-zd {
          color: $--color-text-secondary;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      .tags {
        margin-top: 6px;
        display: flex;
        flex-flow: wrap;
        justify-content: flex-start;
        gap: 8px;
        align-items: center;
      }

      .card-bottom {
        margin-top: 6px;
        height: 32px;
        line-height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #c2cfdc;
        border-radius: 8px;
        .el-icon-delete {
          font-size: 18px;
        }
      }
    }
  }
}
// 弹出内容
.pi-shi {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 2px 6px;
  .title {
    border-left: 4px solid #356ac5;
    padding-left: 6px;
    color: #171c28;
  }
}
</style>
