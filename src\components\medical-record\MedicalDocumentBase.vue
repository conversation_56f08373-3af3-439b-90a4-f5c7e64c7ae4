<template>
  <div class="medical-document-container">
    <div class="medical-document-layout">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <div class="tabs-container">
          <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
            <!-- 记录列表 -->
            <el-tab-pane label="记录列表" name="saved">
              <div class="saved-records-list">
                <el-table
                  v-loading="savedRecordsLoading"
                  :data="savedRecordsList"
                  highlight-current-row
                  height="100%"
                  element-loading-text="加载中..."
                  border
                  stripe
                  size="mini"
                  @row-click="handleSavedRecordClick"
                >
                  <el-table-column prop="jilluSJ" label="记录时间" width="150">
                    <template #default="{ row }">
                      <span class="record-time">{{ row.jilluSJ }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="wenShuMC" :label="documentNameLabel" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span class="record-name" :class="{ 'red-text': shouldShowRedText(row) }">
                        {{ row.wenShuMC }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 文书列表 -->
            <el-tab-pane :label="documentTabLabel" name="authorization">
              <div class="document-content">
                <!-- 搜索和切换区域 -->
                <div class="search-section">
                  <div class="search-input-area">
                    <el-radio-group
                      v-model="isSpecialtyMode"
                      size="mini"
                      @change="handleSpecialtyModeChange"
                    >
                      <el-radio-button :label="true">专科</el-radio-button>
                      <el-radio-button :label="false">通用</el-radio-button>
                    </el-radio-group>
                    <el-input
                      v-model="searchKeyword"
                      placeholder="请输入拼音码或者中文检索"
                      size="mini"
                      clearable
                      @keyup.enter.native="handleSearch"
                    ></el-input>
                  </div>
                </div>

                <!-- 文书列表 -->
                <div class="document-list">
                  <el-table
                    v-loading="documentListLoading"
                    :data="documentList"
                    highlight-current-row
                    height="100%"
                    element-loading-text="加载中..."
                    border
                    stripe
                    size="mini"
                    @row-click="handleDocumentClick"
                  >
                    <el-table-column
                      prop="wenShuMC"
                      :label="documentNameLabel"
                      show-overflow-tooltip
                    >
                      <template #default="{ row }">
                        <span>【新增】</span>
                        <span class="record-name" :class="{ 'red-text': shouldShowRedText(row) }">
                          {{ row.wenShuMC }}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div class="right-panel">
        <div class="right-header">
          <div class="header-content">
            <div class="record-title">
              <span v-if="currentRecord">
                {{ currentRecord.wenShuMC }}
              </span>
              <span v-else>{{ emptySelectionText }}</span>
            </div>
            <div v-if="currentRecord" class="action-buttons">
              <el-button
                v-for="button in actionButtons"
                :key="button.action"
                type="primary"
                size="mini"
                @click="handleAction(button.action)"
              >
                {{ button.label }}
              </el-button>
            </div>
          </div>
        </div>
        <div class="iframe-container">
          <iframe
            v-if="iframeUrl"
            ref="contentIframe"
            :src="iframeUrl"
            frameborder="0"
            @load="handleIframeLoad"
          ></iframe>
          <div v-else class="no-content">
            <i class="el-icon-document"></i>
            <p>{{ noContentText }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getAllYiShuXieWenShuByBLID,
  getListByzhuanKeIDAndWenShuLX,
  getZqjlListForSearch
} from '@/api/informed-consent'

export default {
  name: 'MedicalDocumentBase',
  props: {
    // 文书类型
    wenShuLX: {
      type: String,
      required: true
    },
    documentTabLabel: {
      type: String,
      default: '文书列表'
    },
    // 文书名称标签
    documentNameLabel: {
      type: String,
      default: '文书名称'
    },
    // 未选择文书时的提示文字
    emptySelectionText: {
      type: String,
      default: '请选择文书'
    },
    // 无内容时的提示文字
    noContentText: {
      type: String,
      default: '请从左侧选择文书'
    },
    // 操作按钮配置
    actionButtons: {
      type: Array,
      default: () => [
        { action: 'save', label: '保存' },
        { action: 'delete', label: '删除' },
        { action: 'print', label: '打印' }
      ]
    },
    // 基础URL
    baseUrl: {
      type: String,
      default: 'http://10.41.220.39/ehr'
    }
  },
  data() {
    return {
      activeTab: 'saved',
      savedRecordsList: [],
      savedRecordsLoading: false,
      documentList: [],
      documentListLoading: false,
      searchKeyword: '',
      isSpecialtyMode: true,
      currentRecord: null,
      iframeUrl: ''
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  mounted() {
    this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.fetchSavedRecords()
      await this.fetchDocumentList()
    },

    // 获取已保存记录列表
    async fetchSavedRecords() {
      this.savedRecordsLoading = true
      try {
        const res = await getAllYiShuXieWenShuByBLID({
          bingLiID: this.bingLiID,
          wenShuLXList: this.wenShuLX
        })

        if (res.hasError === 0) {
          this.savedRecordsList = res.data || []
        } else {
          this.$message.error(res.errorMessage || '获取已保存记录失败')
        }
      } catch (error) {
        console.error('获取已保存记录失败', error)
        this.$message.error('获取已保存记录失败')
      } finally {
        this.savedRecordsLoading = false
      }
    },

    // 获取文书列表
    async fetchDocumentList() {
      this.documentListLoading = true
      try {
        const res = await getListByzhuanKeIDAndWenShuLX({
          wenShuLX: this.wenShuLX,
          zhuanKeID: this.isSpecialtyMode ? this.zhuanKeID : '0'
        })

        if (res.hasError === 0) {
          this.documentList = res.data || []
        } else {
          this.$message.error(res.errorMessage || '获取文书列表失败')
        }
      } catch (error) {
        console.error('获取文书列表失败', error)
        this.$message.error('获取文书列表失败')
      } finally {
        this.documentListLoading = false
      }
    },

    // 判断是否应该显示红色文字
    shouldShowRedText(row) {
      return row.guidanBZ && row.guidanBZ === '0'
    },

    // 标签页切换
    handleTabClick(tab) {
      this.activeTab = tab.name
      if (tab.name === 'authorization') {
        this.searchKeyword = ''
      }
    },

    // 已保存记录点击
    handleSavedRecordClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
      this.$emit('record-selected', record)
    },

    // 文书点击
    handleDocumentClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
      this.$emit('document-selected', record)
    },

    // 搜索处理
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning('请输入拼音码或者中文检索')
        return
      }

      this.documentListLoading = true
      try {
        const res = await getZqjlListForSearch({
          key: this.searchKeyword,
          wenShuLX: this.wenShuLX
        })

        if (res.hasError === 0) {
          this.documentList = res.data.map((item) => {
            return { ...item, wenShuMC: item.mingChen }
          })
        }
      } catch (error) {
        console.error('搜索失败', error)
        this.$message.error('搜索失败')
      } finally {
        this.documentListLoading = false
      }
    },

    // 处理专科/通用模式切换
    async handleSpecialtyModeChange(value) {
      this.isSpecialtyMode = value
      await this.fetchDocumentList()
    },

    // 设置iframe URL
    setIframeUrl(record) {
      if (!record) {
        this.iframeUrl = ''
        return
      }

      // 根据guidanBZ字段判断URL生成逻辑
      let params = {}
      let pageName = 'blwsdetail.aspx' // 默认页面

      if (record.guidanBZ === '1') {
        params = {
          as_wsid: record.id || 0,
          as_blid: record.bingLiID || this.bingLiID
        }
        pageName = 'zyblwsPdf.aspx'
      } else {
        params = {
          as_blid: this.bingLiID,
          as_gsdm: record.geshiDM || record.geShiDM,
          as_zyid: this.patientInfo.zhuYuanID,
          as_yhid: this.yongHuID,
          as_wsid: record.id || 0,
          as_wslx: record.wenshuLX || record.wenShuLX,
          tmpid: Math.random()
        }
        pageName = 'blwsdetail.aspx'
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .filter(([, value]) => value !== null && value !== undefined && value !== '')
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      this.iframeUrl = `${this.baseUrl}/zyblws/${pageName}?${queryString}`
    },

    // iframe加载完成
    handleIframeLoad() {
      console.log('文书iframe加载完成')
      this.$emit('iframe-loaded')
    },

    // 处理操作按钮点击
    handleAction(action) {
      const iframe = this.$refs.contentIframe
      if (!iframe || !iframe.contentWindow) {
        this.$message.error('iframe未加载完成')
        return
      }

      switch (action) {
        case 'save':
          this.handleSave(iframe)
          break
        case 'delete':
          this.handleDelete(iframe)
          break
        case 'print':
          this.handlePrint(iframe)
          break
        default:
          this.$emit('custom-action', { action, iframe })
      }
    },

    // 保存操作
    async handleSave(iframe) {
      try {
        // 调用iframe内部的保存方法
        if (typeof iframe.contentWindow.ehr_save === 'function') {
          iframe.contentWindow.ehr_save()

          // 保存成功后刷新已保存记录列表
          await this.fetchSavedRecords()
          this.$message.success('保存成功')
          this.$emit('save-success')
        } else {
          this.$message.error('iframe中没有保存方法')
        }
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存出错: ' + (error.message || '未知错误'))
      }
    },

    // 删除操作
    async handleDelete(iframe) {
      try {
        // 确认删除操作
        await this.$confirm(`确定要删除这条${this.documentTabLabel}吗？`, '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 调用iframe内部的删除方法
        if (typeof iframe.contentWindow.ehr_delete === 'function') {
          iframe.contentWindow.ehr_delete()

          // 删除成功后刷新已保存记录列表
          await this.fetchSavedRecords()
          // 清空当前选中记录
          this.currentRecord = null
          this.iframeUrl = ''
          this.$message.success('删除成功')
          this.$emit('delete-success')
        } else {
          this.$message.error('iframe中没有删除方法')
        }
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消删除，不做任何操作
          return
        }
        console.error('删除失败', error)
        this.$message.error('删除出错: ' + (error.message || '未知错误'))
      }
    },

    // 打印操作
    handlePrint(iframe) {
      try {
        // 调用iframe内部的打印方法
        if (typeof iframe.contentWindow.ehr_print === 'function') {
          iframe.contentWindow.ehr_print()
          this.$emit('print-success')
        } else {
          this.$message.error('iframe中没有打印方法')
        }
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印出错: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style scoped lang="scss">
.medical-document-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .medical-document-layout {
    display: flex;
    height: 100%;
    gap: 8px;
    padding: 8px;

    .left-panel {
      width: 380px;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .tabs-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        ::v-deep .el-tabs {
          display: flex;
          flex-direction: column;
          height: 100%;

          .el-tabs__content {
            flex: 1;
            overflow: hidden;
            padding: 10px 0 0;
          }

          .el-tab-pane {
            height: 100%;
          }
        }

        .saved-records-list,
        .document-content {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .search-section {
          padding: 0 10px 10px;

          .search-input-area {
            display: flex;
            align-items: center;
            gap: 8px;

            ::v-deep .el-radio-group {
              min-width: 115px;
            }
          }
        }

        .document-list {
          flex: 1;
          overflow: hidden;
        }

        .red-text {
          color: #f56c6c;
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .right-header {
        padding: 12px;
        border-bottom: 1px solid #ebeef5;

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .record-title {
            font-size: 16px;
            font-weight: bold;
          }

          .action-buttons {
            display: flex;
            gap: 8px;
          }
        }
      }

      .iframe-container {
        flex: 1;
        position: relative;
        overflow: hidden;

        iframe {
          width: 100%;
          height: 100%;
          border: none;
        }

        .no-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c0c4cc;
          }

          p {
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
  }
}
</style>
