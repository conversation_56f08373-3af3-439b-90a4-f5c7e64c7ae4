<template>
  <div class="navbar">
    <div class="container-logo">
      <logo />
    </div>
    <div class="center-menu">
      <header-menu />
    </div>
    <div class="right-menu">
      <div class="tool-item">
        <el-tag class="" effect="dark">病历质控</el-tag>
        <span class="menu-text">字体大小</span>
        <el-dropdown size="mini" placement="bottom">
          <el-button type="primary">
            {{ sizeOptions[$store.state.theme.size] }}
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(label, value) in sizeOptions"
              :key="value"
              :value="value"
              @click.native="$store.dispatch('theme/setSize', value)"
            >
              {{ label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-tooltip :content="!nightMode ? '夜间模式' : '日间模式'" effect="dark" placement="bottom">
        <div class="right-menu-item hover-effect" @click="nightClick">
          <svg-icon :icon-class="!nightMode ? 'night' : 'day'" class="icon-test" />
        </div>
      </el-tooltip>
      <el-tooltip content="通知" effect="dark" placement="bottom">
        <bell-pendant :user-state="$store.state.user" :get-token="getToken"></bell-pendant>
      </el-tooltip>
      <el-tooltip content="设置" effect="dark" placement="bottom">
        <el-dropdown class="right-menu-dropdown-item" trigger="click">
          <div class="right-menu-item hover-effect">
            <svg-icon icon-class="setting" class="icon-test" />
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <el-dropdown-item>
              <div @click="openPersonal">个性化</div>
            </el-dropdown-item>
            <el-dropdown-item>
              <div @click="report">问题反馈</div>
            </el-dropdown-item>
            <el-dropdown-item @click.native="logout">
              <span style="display: block">退出系统</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-tooltip>
      <el-tooltip content="锁定屏幕" effect="dark" placement="bottom">
        <!-- <lock-screen class="right-menu-item hover-effect" /> -->
        <div class="right-menu-item hover-effect" @click="lockClick">
          <svg-icon icon-class="lock" class="icon-test" />
        </div>
      </el-tooltip>
      <div class="campus" @click="showZhuanKeList">
        <span class="menu-text">
          {{ initInfo.yongHuZKMC }}
        </span>

        <span v-if="initInfo.yongHuXM" class="menu-text">
          {{ `-` + initInfo.yongHuXM }}
        </span>
      </div>
      <div class="avatar-container">
        <el-dropdown
          class="right-menu-dropdown-item avatar-right-menu-dropdown-item"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <img :src="avatar" class="user-avatar" alt="" />
          </div>
          <template #dropdown>
            <el-dropdown-menu class="user-dropdown">
              <!--              <avatar-uploader-->
              <!--                ref="upload"-->
              <!--                :request="request"-->
              <!--                :yong-hu-i-d="yongHuID"-->
              <!--                @success="handleSuccess"-->
              <!--              >-->
              <el-dropdown-item @click.native="avatarVisible = true">
                <span style="display: block">上传头像</span>
              </el-dropdown-item>
              <!--              </avatar-uploader>-->
              <el-dropdown-item @click.native="logout">
                <span style="display: block">退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <default-dialog
        :visible.sync="personalVisible"
        width="470px"
        title="个性化设置"
        confirm-button-text="保存"
        @confirm="savePersonal"
      >
        <div class="checkbox-container">
          <div class="title table-color-dark">请选择手机端接收移动审批消息类型 默认选择：</div>
          <el-checkbox
            v-model="checkAll"
            class="checkbox-all table-color-light"
            :indeterminate="isIndeterminate"
            @change="handleCheckAll"
          >
            全选
          </el-checkbox>
          <el-checkbox-group
            v-model="checkList"
            class="checkbox-setting"
            @change="handleChangeChecked"
          >
            <el-checkbox
              v-for="(item, index) in checkBoxSetting"
              :key="index"
              :class="index % 2 ? 'table-color-light' : 'table-color-dark'"
              :label="item.value"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="radio-container">
          <div class="title table-color-dark">请选择个性化记录 默认选择：</div>
          <el-radio-group v-model="radio">
            <el-radio
              v-for="(item, index) in radioSetting"
              :key="index"
              :class="index % 2 ? 'table-color-dark' : 'table-color-light'"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </div>
      </default-dialog>
      <error-reporting
        :request="request"
        :ying-yong-key="yingYongKey"
        :visible.sync="errorReportingVisible"
      ></error-reporting>
      <avatar-cropper-uploader
        :avatar-img="avatar"
        :request="request"
        :visible.sync="avatarVisible"
        :yong-hu-i-d="yongHuID"
        @success="handleSuccess"
        @error="handleError"
      />
      <default-dialog
        append-to-body
        :visible.sync="zhuanKeListVisible"
        width="600px"
        title="修改科室信息"
        @confirm="handleZhuanKeConfirm"
      >
        <el-descriptions class="custom-descriptions" :column="1" border size="mini">
          <el-descriptions-item label="请选择专科">
            <el-select v-model="zhuanKeID" placeholder="请选择专科" autofocus filterable>
              <el-option
                v-for="item in zhuanKeList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID.toString()"
              />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
      </default-dialog>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import { mapGetters, mapState } from 'vuex'
import svgIcon from '@/components/SvgIcon'
import { AvatarCropperUploader, ErrorReporting } from 'wyyy-component'
import BellPendant from '@/components/BellPendant/index.vue'
import { changeZhuanKe, changeZhuanKeLog, getUserImgById, getZhuanKeList } from '@/api/user'
import { getToken, getYongHuID } from '@/utils/auth'
import Logo from './components/Logo.vue'
import HeaderMenu from './components/HeaderMenu'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import store from '@/store'
import { EventBus } from '@/utils/event-bus'
import { getAlertMessages, savePersonalization } from '@/api/patient'
export default {
  components: {
    Logo,
    HeaderMenu,
    svgIcon,
    ErrorReporting,
    BellPendant,
    AvatarCropperUploader,
    DefaultDialog
  },
  data() {
    return {
      request,
      sizeOptions: {
        verysmall: '超小',
        standard: '标准',
        large: '加大',
        huge: '特大'
      },
      personalVisible: false,
      errorReportingVisible: false,
      avatarVisible: false,
      getToken,
      zhuanKeListVisible: false,
      zhuanKeList: [],
      zhuanKeID: '',
      // 个性化
      checkAll: false,
      checkBoxSetting: [
        { value: '04', label: '手术通知单' },
        { value: '05', label: '特殊抗菌药物会诊' },
        { value: '06', label: '三联抗菌药物会诊' },
        { value: '07', label: '用血审批' },
        { value: '08', label: '会诊审批' }
      ],
      isIndeterminate: false,
      checkList: [],
      radio: null,
      radioSetting: [
        { value: '4', label: '我的病人' },
        { value: '1', label: '治疗组病人' },
        { value: '3', label: '专科治疗组' },
        { value: '2', label: '专科病区病人' },
        { value: '5', label: '公共床位' },
        { value: '6', label: '科研组病人' }
      ]
    }
  },
  computed: {
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo,
      geXingHua: ({ patient }) => patient.geXingHua,
      jianYiSP: ({ patient }) => patient.jianYiSP,
      nightMode: ({ theme }) => theme.nightMode,
      yingYongKey: ({ user }) => user.app_key
    }),
    ...mapGetters(['sidebar', 'avatar']),
    yongHuID() {
      return getYongHuID()?.toString()
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    nightClick() {
      this.$store.dispatch('theme/toggleNightMode')
    },
    lockClick() {
      console.log('屏幕锁屏')
      this.$store.dispatch('lockscreen/setLock', true)
    },
    logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('user/logOut')
        })
        .catch(() => {})
    },
    openPersonal() {
      // 移动建议审批
      this.checkList = this.jianYiSP[0].neiRong?.split(',')
      const checkCount = this.checkList.length
      this.checkAll = checkCount === this.checkBoxSetting.length
      this.isIndeterminate = checkCount > 0 && checkCount < this.checkBoxSetting.length
      // 个性化
      this.radio = this.geXingHua[0].neiRong
      this.personalVisible = true
    },
    handleCheckAll(val) {
      this.checkList = val ? this.checkBoxSetting : []
      this.isIndeterminate = false
    },
    handleChangeChecked(value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.checkBoxSetting.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.checkBoxSetting.length
    },
    savePersonal() {
      const jianYiParams = {
        yeWuDM: '0005',
        neiRong: this.checkList.join(','),
        yiShengYHID: this.yongHuID,
        caoZuoZheID: this.yongHuID
      }
      const geXingHuaParams = {
        yeWuDM: '0001',
        neiRong: this.radio,
        yiShengYHID: this.yongHuID,
        caoZuoZheID: this.yongHuID
      }
      Promise.all([savePersonalization(jianYiParams), savePersonalization(geXingHuaParams)]).then(
        (res) => {
          if (res[0]?.hasError === 0 && res[1]?.hasError === 0) {
            this.$message.success('保存成功')
            this.personalVisible = false
          }
        }
      )
    },
    report() {
      this.errorReportingVisible = true
    },
    getAvatar() {
      getUserImgById({ yongHuID: getYongHuID() }).then((rsp) => {
        rsp?.data?.touXiangDZ && this.$store.commit('user/SET_AVATAR', rsp.data.touXiangDZ)
      })
    },
    handleSuccess(response) {
      this.getAvatar()
      this.$message.success('上传成功!')
      this.avatarVisible = false
      // 处理上传成功的逻辑
      console.log('上传成功:', response)
    },
    handleError(error) {
      console.log('上传失败:', error)
    },
    showZhuanKeList() {
      getZhuanKeList().then((res) => {
        if (res.hasError !== 0) {
          return
        }
        this.zhuanKeList = res.data
        this.zhuanKeID = this.initInfo.zhuanKeID || ''
        this.zhuanKeListVisible = true
      })
    },
    async handleZhuanKeConfirm() {
      const res = await changeZhuanKe({ zhuanKeID: this.zhuanKeID })
      if (res.hasError !== 0) {
        return
      }
      const log = await changeZhuanKeLog({
        qieHuanQianZKID: this.initInfo.zhuanKeID,
        qieHuanHouZKID: this.zhuanKeID
      })
      if (log.hasError !== 0) {
        return
      }
      this.zhuanKeListVisible = false
      await this.$alert('切换成功，需要重新加载页面', '温馨提醒', {
        closeOnPressEscape: false,
        closeOnClickModal: false,
        showClose: false,
        confirmButtonText: '确定'
      }).then(() => {
        this.$router.push('/home')
        window.location.reload()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';
.navbar {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
  .container-logo {
    width: var(--side-bar-width);
    height: 52px;
    background-color: $color-primary;
  }
  .center-menu {
    flex: 1;
  }

  .right-menu {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background: #ffffff;

    .menu-text {
      font-size: 12px;
      font-weight: 500;
      // color: rgba(212, 80, 80, 0.025);
      color: rgb(23 28 40 / 65%);
    }

    .tool-item {
      margin: 0 10px;

      .menu-text {
        margin-right: 4px;
        margin-left: 10px;
      }
    }

    .menu-select {
      cursor: pointer;

      ::v-deep .el-input {
        &__inner {
          width: 40px;
          font: var(--font-medium);
          font-size: var(--font-size-regular);
          color: #fff;
          text-align: center;
          background: var(--color-primary);
          border-radius: var(--border-radius-base);
        }

        &__suffix {
          display: none;
        }
      }
    }

    &:focus {
      outline: none;
    }

    .lock-screen {
      font-size: 24px;
      color: white;
    }

    .right-menu-dropdown-item {
      display: flex;
      align-items: center;
      height: 52px;
    }

    ::v-deep .right-menu-item {
      display: flex;
      align-items: center;
      padding: 0 7px;
      font-size: 18px;
      color: rgb(23 28 40 / 45%);

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgb(0 0 0 / 2.5%);
        }
      }
    }

    .msg-badge {
      cursor: pointer;

      ::v-deep .el-badge__content {
        right: 14px;
        min-width: 17px;
        max-width: 25px;
        height: 17px;
        padding: 0 2px;
        line-height: 14px;
        background-color: #d54941;
      }
    }

    .campus {
      padding: 0 7px;
      cursor: pointer;
    }

    .avatar-right-menu-dropdown-item {
      display: flex;
      align-items: center;
    }

    .avatar-container {
      /* margin-right: 30px; */
      padding: 0 7px;

      .avatar-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 26px;
        height: 26px;
        vertical-align: baseline;
        background-color: rgb(23 28 40 / 8%);
        border-radius: 50%;

        .user-avatar {
          width: 100%;
          height: 100%;
          cursor: pointer;
          border-radius: 50%;
        }

        /* .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        } */
      }
    }
  }
}

.checkbox-container,
.radio-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin: 0 6px;
  .title {
    height: 40px;
    padding-left: 16px;
    margin: 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dcdfe6;
  }
}
.table-color-dark {
  background: #eaf0f9;
}
.table-color-light {
  background: #f6f6f6;
}
.checkbox-container {
  .checkbox-setting {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .el-checkbox {
    height: 40px;
    padding-left: 16px;
    margin: 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #dcdfe6;
    &:last-child {
      border-bottom: none;
    }
  }
}
.radio-container {
  margin-top: 16px;
  .el-radio-group {
    display: flex;
    flex-direction: column;
    justify-content: center;
    .el-radio {
      height: 40px;
      padding-left: 16px;
      margin: 0;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #dcdfe6;
      &:last-child {
        border-bottom: none;
      }
    }
  }
}
</style>
