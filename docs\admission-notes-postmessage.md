# 入院记录组件 PostMessage 通信机制

## 概述

为了解决入院记录组件(AdmissionNotes.vue)中直接调用iframe内容窗口方法遇到的跨域安全限制问题，我们实现了基于postMessage的通信机制。

## 问题背景

原有代码直接调用iframe方法：
```javascript
iframe.contentWindow.ehr_save()
iframe.contentWindow.ehr_delete()
iframe.contentWindow.ehr_print()
```

这种方式在跨域环境下会遇到安全限制，导致操作失败。

## 解决方案

使用HTML5的postMessage API实现父页面与iframe之间的安全通信。

### 通信流程

1. **父页面发送指令**：AdmissionNotes.vue通过postMessage发送操作指令给iframe
2. **iframe接收处理**：iframe页面监听message事件，执行相应操作
3. **iframe返回结果**：iframe执行完成后，通过postMessage将结果返回给父页面
4. **父页面处理结果**：AdmissionNotes.vue接收结果，更新UI状态

## 消息格式定义

### 父页面 → iframe

```javascript
{
  type: 'admission_note_save' | 'admission_note_delete' | 'admission_note_print',
  recordId: string | number,  // 记录ID
  action: 'save' | 'delete' | 'print'
}
```

### iframe → 父页面

```javascript
{
  type: 'admission_note_save_result' | 'admission_note_delete_result' | 'admission_note_print_result',
  recordId: string | number,  // 记录ID
  success: boolean,           // 操作是否成功
  message?: string,           // 成功或失败的消息
  error?: string              // 错误信息（失败时）
}
```

## 代码修改详情

### AdmissionNotes.vue 修改

#### 1. 添加生命周期钩子

```javascript
mounted() {
  this.fetchInitData()
  this.addMessageListener()  // 新增
},
beforeDestroy() {
  this.removeMessageListener()  // 新增
}
```

#### 2. 添加消息监听器

```javascript
// 添加postMessage监听器
addMessageListener() {
  window.addEventListener('message', this.handlePostMessage, false)
},

// 移除postMessage监听器
removeMessageListener() {
  window.removeEventListener('message', this.handlePostMessage, false)
},

// 处理iframe发送的postMessage消息
handlePostMessage(event) {
  // 消息验证和处理逻辑
}
```

#### 3. 修改操作方法

**保存方法**：
```javascript
handleSaveClick(recordId) {
  const iframe = document.getElementById(`iframe_${recordId}`)
  const message = {
    type: 'admission_note_save',
    recordId: recordId,
    action: 'save'
  }
  iframe.contentWindow.postMessage(message, '*')
}
```

**删除方法**：
```javascript
handleDeleteClick(recordId) {
  // 确认对话框后发送删除指令
  const message = {
    type: 'admission_note_delete',
    recordId: recordId,
    action: 'delete'
  }
  iframe.contentWindow.postMessage(message, '*')
}
```

**打印方法**：
```javascript
handlePrintClick(recordId) {
  const message = {
    type: 'admission_note_print',
    recordId: recordId,
    action: 'print'
  }
  iframe.contentWindow.postMessage(message, '*')
}
```

#### 4. 添加结果处理方法

```javascript
// 处理保存结果
async handleSaveResult(recordId, success, message, error) {
  if (success) {
    // 更新记录状态，刷新列表
    await this.refreshYiShuXieWenShuList()
    this.$message.success(message || '保存成功')
  } else {
    this.$message.error(error || '保存失败')
  }
}

// 处理删除结果
async handleDeleteResult(recordId, success, message, error) {
  if (success) {
    // 处理临时记录或已保存记录的删除
    await this.refreshYiShuXieWenShuList()
    this.$message.success(message || '删除成功')
  } else {
    this.$message.error(error || '删除失败')
  }
}

// 处理打印结果
handlePrintResult(recordId, success, message, error) {
  if (success) {
    this.$message.success(message || '打印成功')
  } else {
    this.$message.error(error || '打印失败')
  }
}
```

### iframe页面修改

iframe页面需要添加消息监听器和处理逻辑，详见 `docs/iframe-postmessage-example.js`

## 错误处理机制

1. **消息验证**：验证消息格式和来源
2. **异常捕获**：所有操作都包含try-catch
3. **超时处理**：可以添加超时机制防止无响应
4. **降级方案**：保持原有方法的兼容性

## 安全考虑

1. **Origin验证**：可以根据需要添加origin验证
2. **消息类型验证**：只处理特定类型的消息
3. **参数验证**：验证消息参数的有效性

## 调试支持

1. **控制台日志**：关键操作都有console.log输出
2. **错误信息**：详细的错误信息便于排查问题
3. **消息追踪**：可以追踪消息的发送和接收

## 兼容性

- 支持所有现代浏览器
- 保持与原有代码的向后兼容性
- 不影响其他组件的正常使用

## 使用建议

1. **测试验证**：在不同浏览器和环境下测试通信机制
2. **错误监控**：添加错误监控和上报机制
3. **性能优化**：避免频繁的消息传递
4. **文档维护**：保持文档与代码的同步更新
