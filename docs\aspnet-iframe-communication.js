/**
 * ASP.NET Web Forms 医疗文书通用PostMessage通信脚本
 * 
 * 适用于 blwsdetail.aspx 等ASP.NET页面
 * 支持所有医疗文书类型的统一通信机制
 */

// 全局命名空间，避免与ASP.NET控件冲突
window.MedicalDocumentCommunication = (function() {
    'use strict';

    // 消息类型常量
    var MESSAGE_TYPES = {
        ACTION: 'medical_document_action',
        RESULT: 'medical_document_result'
    };

    // 操作类型常量
    var ACTION_TYPES = {
        SAVE: 'save',
        DELETE: 'delete',
        PRINT: 'print',
        PREVIEW: 'preview',
        EXPORT: 'export',
        VALIDATE: 'validate',
        SUBMIT: 'submit',
        APPROVE: 'approve',
        REJECT: 'reject',
        CUSTOM: 'custom'
    };

    // 错误代码常量
    var ERROR_CODES = {
        METHOD_NOT_FOUND: 'METHOD_NOT_FOUND',
        PERMISSION_DENIED: 'PERMISSION_DENIED',
        VALIDATION_FAILED: 'VALIDATION_FAILED',
        NETWORK_ERROR: 'NETWORK_ERROR',
        SERVER_ERROR: 'SERVER_ERROR',
        UNKNOWN_ERROR: 'UNKNOWN_ERROR'
    };

    // 私有变量
    var isInitialized = false;
    var currentModule = null;

    /**
     * 初始化通信模块
     * @param {string} moduleType 模块类型
     */
    function initialize(moduleType) {
        if (isInitialized) {
            return;
        }

        currentModule = moduleType || detectModuleType();
        
        // 添加消息监听器
        if (window.addEventListener) {
            window.addEventListener('message', handleParentMessage, false);
        } else if (window.attachEvent) {
            // IE8兼容性
            window.attachEvent('onmessage', handleParentMessage);
        }

        isInitialized = true;
        console.log('[' + currentModule + '] ASP.NET医疗文书通信已初始化');
    }

    /**
     * 自动检测模块类型（基于URL参数或页面特征）
     * @returns {string}
     */
    function detectModuleType() {
        var url = window.location.href.toLowerCase();
        var params = getUrlParams();

        // 根据URL参数判断文书类型
        if (params.as_wslx) {
            var wslx = params.as_wslx;
            // 根据文书类型代码映射到模块类型
            var moduleMap = {
                '01': 'admission_notes',      // 入院记录
                '02': 'progress_notes',       // 病程记录
                '03': 'discharge_summary',    // 出院记录
                '04': 'operative_records',    // 手术记录
                '05': 'informed_consent'      // 知情记录
            };
            return moduleMap[wslx] || 'unknown';
        }

        // 根据URL路径判断
        if (url.indexOf('blwsdetail') >= 0) {
            return 'medical_document';
        }

        return 'unknown';
    }

    /**
     * 获取URL参数
     * @returns {object}
     */
    function getUrlParams() {
        var params = {};
        var search = window.location.search.substring(1);
        if (search) {
            var pairs = search.split('&');
            for (var i = 0; i < pairs.length; i++) {
                var pair = pairs[i].split('=');
                if (pair.length === 2) {
                    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
                }
            }
        }
        return params;
    }

    /**
     * 处理来自父页面的消息
     * @param {MessageEvent} event 
     */
    function handleParentMessage(event) {
        var data = event.data;
        
        // 验证消息格式
        if (!isValidMessage(data)) {
            return;
        }

        // 只处理操作消息
        if (data.messageType !== MESSAGE_TYPES.ACTION) {
            return;
        }

        console.log('[' + currentModule + '] 收到父页面消息:', data);

        // 执行操作
        executeAction(data);
    }

    /**
     * 验证消息格式
     * @param {any} data 
     * @returns {boolean}
     */
    function isValidMessage(data) {
        return data && 
               typeof data === 'object' && 
               data.messageType && 
               data.module && 
               data.action && 
               data.messageId;
    }

    /**
     * 执行操作
     * @param {object} message 
     */
    function executeAction(message) {
        var action = message.action;
        var recordId = message.recordId;
        var payload = message.payload || {};
        
        var result = {
            success: false,
            message: '',
            error: '',
            errorCode: '',
            data: null
        };

        try {
            switch (action) {
                case ACTION_TYPES.SAVE:
                    result = executeSave(payload);
                    break;
                case ACTION_TYPES.DELETE:
                    result = executeDelete(payload);
                    break;
                case ACTION_TYPES.PRINT:
                    result = executePrint(payload);
                    break;
                case ACTION_TYPES.CUSTOM:
                    result = executeCustomAction(payload.customAction, payload);
                    break;
                default:
                    result.error = '不支持的操作类型: ' + action;
                    result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
            }
        } catch (e) {
            result.success = false;
            result.error = e.message || '操作执行失败';
            result.errorCode = ERROR_CODES.UNKNOWN_ERROR;
            console.error('[' + currentModule + '] 操作执行异常:', e);
        }

        // 发送结果给父页面
        sendResult(message, result);
    }

    /**
     * 执行保存操作
     * @param {object} payload 
     * @returns {object}
     */
    function executeSave(payload) {
        var result = { success: false, message: '', error: '' };

        // 检查是否存在保存方法
        if (typeof window.ehr_save === 'function') {
            try {
                window.ehr_save();
                result.success = true;
                result.message = '保存成功';
            } catch (e) {
                result.error = '保存操作失败: ' + e.message;
            }
        } else if (typeof window.save === 'function') {
            // 备用保存方法
            try {
                window.save();
                result.success = true;
                result.message = '保存成功';
            } catch (e) {
                result.error = '保存操作失败: ' + e.message;
            }
        } else {
            result.error = '页面中没有保存方法';
            result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
        }

        return result;
    }

    /**
     * 执行删除操作
     * @param {object} payload 
     * @returns {object}
     */
    function executeDelete(payload) {
        var result = { success: false, message: '', error: '' };

        if (typeof window.ehr_delete === 'function') {
            try {
                window.ehr_delete();
                result.success = true;
                result.message = '删除成功';
            } catch (e) {
                result.error = '删除操作失败: ' + e.message;
            }
        } else if (typeof window.deleteRecord === 'function') {
            try {
                window.deleteRecord();
                result.success = true;
                result.message = '删除成功';
            } catch (e) {
                result.error = '删除操作失败: ' + e.message;
            }
        } else {
            result.error = '页面中没有删除方法';
            result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
        }

        return result;
    }

    /**
     * 执行打印操作
     * @param {object} payload 
     * @returns {object}
     */
    function executePrint(payload) {
        var result = { success: false, message: '', error: '' };

        if (typeof window.ehr_print === 'function') {
            try {
                window.ehr_print();
                result.success = true;
                result.message = '打印成功';
            } catch (e) {
                result.error = '打印操作失败: ' + e.message;
            }
        } else if (typeof window.print === 'function') {
            try {
                window.print();
                result.success = true;
                result.message = '打印成功';
            } catch (e) {
                result.error = '打印操作失败: ' + e.message;
            }
        } else {
            result.error = '页面中没有打印方法';
            result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
        }

        return result;
    }

    /**
     * 执行自定义操作
     * @param {string} customAction 
     * @param {object} payload 
     * @returns {object}
     */
    function executeCustomAction(customAction, payload) {
        var result = { success: false, message: '', error: '' };

        if (typeof window[customAction] === 'function') {
            try {
                var customResult = window[customAction](payload.params);
                result.success = true;
                result.message = customAction + '执行成功';
                result.data = customResult;
            } catch (e) {
                result.error = customAction + '执行失败: ' + e.message;
            }
        } else {
            result.error = '页面中没有方法: ' + customAction;
            result.errorCode = ERROR_CODES.METHOD_NOT_FOUND;
        }

        return result;
    }

    /**
     * 发送结果给父页面
     * @param {object} originalMessage 
     * @param {object} result 
     */
    function sendResult(originalMessage, result) {
        var resultMessage = {
            messageType: MESSAGE_TYPES.RESULT,
            requestMessageId: originalMessage.messageId,
            module: originalMessage.module,
            action: originalMessage.action,
            recordId: originalMessage.recordId,
            result: result,
            timestamp: new Date().getTime()
        };

        try {
            if (window.parent && window.parent !== window) {
                window.parent.postMessage(resultMessage, '*');
                console.log('[' + currentModule + '] 发送结果给父页面:', resultMessage);
            }
        } catch (e) {
            console.error('[' + currentModule + '] 发送结果失败:', e);
        }
    }

    // 公开接口
    return {
        initialize: initialize,
        MESSAGE_TYPES: MESSAGE_TYPES,
        ACTION_TYPES: ACTION_TYPES,
        ERROR_CODES: ERROR_CODES
    };
})();

// ASP.NET页面加载完成后自动初始化
// 兼容不同的页面加载事件
(function() {
    function autoInitialize() {
        // 延迟初始化，确保页面完全加载
        setTimeout(function() {
            window.MedicalDocumentCommunication.initialize();
        }, 100);
    }

    // 标准浏览器
    if (document.addEventListener) {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', autoInitialize);
        } else {
            autoInitialize();
        }
    } 
    // IE8兼容性
    else if (document.attachEvent) {
        if (document.readyState === 'complete') {
            autoInitialize();
        } else {
            document.attachEvent('onreadystatechange', function() {
                if (document.readyState === 'complete') {
                    autoInitialize();
                }
            });
        }
    }
})();
