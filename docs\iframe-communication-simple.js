/**
 * 入院记录iframe通信简化版本
 * 
 * 将此代码添加到iframe页面（如blwsdetail.aspx）的底部
 * 用于与AdmissionNotes.vue父页面进行postMessage通信
 */

// 立即执行函数，避免全局变量污染
(function() {
  'use strict';

  console.log('入院记录iframe通信模块加载');

  // 添加消息监听器
  window.addEventListener('message', function(event) {
    // 基本验证
    if (!event.data || typeof event.data !== 'object') {
      return;
    }

    const { type, recordId, action } = event.data;

    // 只处理入院记录相关消息
    if (!type || !type.startsWith('admission_note_')) {
      return;
    }

    console.log('iframe收到消息:', event.data);

    // 根据消息类型执行相应操作
    switch (type) {
      case 'admission_note_save':
        executeAction('save', recordId);
        break;
      case 'admission_note_delete':
        executeAction('delete', recordId);
        break;
      case 'admission_note_print':
        executeAction('print', recordId);
        break;
    }
  }, false);

  /**
   * 执行具体操作
   * @param {string} action - 操作类型：save, delete, print
   * @param {string|number} recordId - 记录ID
   */
  function executeAction(action, recordId) {
    let success = false;
    let message = '';
    let error = '';

    try {
      switch (action) {
        case 'save':
          if (typeof window.ehr_save === 'function') {
            window.ehr_save();
            success = true;
            message = '保存成功';
          } else {
            error = 'iframe中没有保存方法';
          }
          break;

        case 'delete':
          if (typeof window.ehr_delete === 'function') {
            window.ehr_delete();
            success = true;
            message = '删除成功';
          } else {
            error = 'iframe中没有删除方法';
          }
          break;

        case 'print':
          if (typeof window.ehr_print === 'function') {
            window.ehr_print();
            success = true;
            message = '打印成功';
          } else {
            error = 'iframe中没有打印方法';
          }
          break;

        default:
          error = '未知的操作类型: ' + action;
      }
    } catch (e) {
      success = false;
      error = e.message || '操作执行失败';
      console.error('iframe操作失败:', e);
    }

    // 发送结果给父页面
    sendResult(action, recordId, success, message, error);
  }

  /**
   * 发送操作结果给父页面
   * @param {string} action - 操作类型
   * @param {string|number} recordId - 记录ID
   * @param {boolean} success - 是否成功
   * @param {string} message - 成功消息
   * @param {string} error - 错误消息
   */
  function sendResult(action, recordId, success, message, error) {
    const resultMessage = {
      type: `admission_note_${action}_result`,
      recordId: recordId,
      success: success,
      message: message,
      error: error
    };

    try {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage(resultMessage, '*');
        console.log('iframe发送结果:', resultMessage);
      }
    } catch (e) {
      console.error('发送结果失败:', e);
    }
  }

})();

/**
 * 使用说明：
 * 
 * 1. 将此代码复制到iframe页面的<script>标签中
 * 2. 确保在页面加载完成后执行
 * 3. 原有的ehr_save、ehr_delete、ehr_print方法保持不变
 * 4. 此代码会自动处理与父页面的通信
 * 
 * 示例HTML：
 * <script>
 *   // 将上面的代码粘贴到这里
 * </script>
 */
